/**
 * Authentication service for handling user registration, login, and logout
 */

import { ref } from 'vue';
import { API_BASE_URL } from './api';

// User interface
export interface User {
  id: number;
  name: string;
  email: string;
  role?: string;
  agency_id?: number | null;
  groups?: {
    id: number;
    name: string;
    type: string;
    is_guide: boolean;
  }[];
  created_at: string;
  updated_at: string;
}

// Authentication state
export const currentUser = ref<User | null>(null);
export const isAuthenticated = ref<boolean>(false);
export const isGuestMode = ref<boolean>(false);
export const authToken = ref<string | null>(null);
export const authError = ref<string | null>(null);

// Load authentication state from localStorage on startup
const initAuth = () => {
  const storedToken = localStorage.getItem('auth_token');
  const storedUser = localStorage.getItem('user');
  const storedGuestMode = localStorage.getItem('guest_mode');

  if (storedToken && storedUser) {
    try {
      authToken.value = storedToken;
      currentUser.value = JSON.parse(storedUser);
      isAuthenticated.value = true;
      isGuestMode.value = false;
    } catch (error) {
      console.error('Error parsing stored user:', error);
      clearAuth();
    }
  } else if (storedGuestMode === 'true') {
    // Restore guest mode if it was active
    isGuestMode.value = true;
    isAuthenticated.value = false;
    currentUser.value = null;
  }
};

// Clear authentication state
const clearAuth = () => {
  localStorage.removeItem('auth_token');
  localStorage.removeItem('user');
  localStorage.removeItem('guest_mode');
  currentUser.value = null;
  isAuthenticated.value = false;
  isGuestMode.value = false;
  authToken.value = null;
};

/**
 * Enter guest mode - allows access without authentication
 */
export const enterGuestMode = (): void => {
  clearAuth(); // Clear any existing auth state
  isGuestMode.value = true;
  localStorage.setItem('guest_mode', 'true');
};

// Initialize authentication state
initAuth();

/**
 * Register a new user
 * @param name - User's name
 * @param email - User's email
 * @param password - User's password
 * @param passwordConfirmation - Password confirmation
 * @returns Promise<User> - The registered user
 */
export const register = async (
  name: string,
  email: string,
  password: string,
  passwordConfirmation: string
): Promise<User> => {
  try {
    authError.value = null;

    const response = await fetch(`${API_BASE_URL}/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify({
        name,
        email,
        password,
        password_confirmation: passwordConfirmation,
      }),
    });

    const data = await response.json();

    if (!response.ok) {
      if (data.errors) {
        const errorMessages = Object.values(data.errors).flat() as string[];
        throw new Error(errorMessages.join(' '));
      }
      throw new Error('Registration failed');
    }

    // Store authentication data
    authToken.value = data.access_token;
    currentUser.value = data.user;
    isAuthenticated.value = true;

    // Save to localStorage
    localStorage.setItem('auth_token', data.access_token);
    localStorage.setItem('user', JSON.stringify(data.user));

    return data.user;
  } catch (error) {
    if (error instanceof Error) {
      authError.value = error.message;
    } else {
      authError.value = 'An unknown error occurred during registration';
    }
    throw error;
  }
};

/**
 * Login a user
 * @param email - User's email
 * @param password - User's password
 * @returns Promise<User> - The logged in user
 */
export const login = async (email: string, password: string): Promise<User> => {
  try {
    authError.value = null;
    console.log('Attempting login with:', { email });

    const response = await fetch(`${API_BASE_URL}/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify({
        email,
        password,
      }),
    });

    console.log('Login response status:', response.status);

    // Handle server errors
    if (response.status >= 500) {
      console.error('Server error during login:', response.status);
      throw new Error('Error del servidor. Por favor, inténtelo de nuevo más tarde.');
    }

    let data;
    try {
      data = await response.json();
    } catch (e) {
      console.error('Error parsing login response:', e);
      throw new Error('Error en la respuesta del servidor. Por favor, inténtelo de nuevo.');
    }

    if (!response.ok) {
      if (data.errors) {
        const errorMessages = Object.values(data.errors).flat() as string[];
        throw new Error(errorMessages.join(' '));
      }
      throw new Error(data.message || 'Login failed');
    }

    console.log('Login successful, received data:', { hasToken: !!data.access_token, hasUser: !!data.user });

    // Store authentication data
    authToken.value = data.access_token;
    currentUser.value = data.user;
    isAuthenticated.value = true;

    // Save to localStorage
    localStorage.setItem('auth_token', data.access_token);
    localStorage.setItem('user', JSON.stringify(data.user));

    return data.user;
  } catch (error) {
    if (error instanceof Error) {
      authError.value = error.message;
    } else {
      authError.value = 'An unknown error occurred during login';
    }
    throw error;
  }
};

/**
 * Logout the current user
 * @returns Promise<void>
 */
export const logout = async (): Promise<void> => {
  try {
    if (!authToken.value) {
      clearAuth();
      return;
    }

    const response = await fetch(`${API_BASE_URL}/logout`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${authToken.value}`,
      },
    });

    if (!response.ok) {
      console.error('Logout failed on server, but proceeding with local logout');
    }
  } catch (error) {
    console.error('Error during logout:', error);
  } finally {
    clearAuth();
  }
};

/**
 * Get the current authenticated user
 * @returns Promise<User> - The current user
 */
export const getCurrentUser = async (): Promise<User | null> => {
  try {
    if (!authToken.value) {
      return null;
    }

    const response = await fetch(`${API_BASE_URL}/user/profile`, {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${authToken.value}`,
      },
    });

    if (!response.ok) {
      if (response.status === 401) {
        // Token is invalid or expired
        clearAuth();
        return null;
      }
      throw new Error('Failed to get user data');
    }

    const userData = await response.json();

    // Update current user data
    currentUser.value = userData;

    // Update localStorage
    localStorage.setItem('user', JSON.stringify(userData));

    return userData;
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
};

/**
 * Get the user's groups
 * @returns Promise<User['groups']> - The user's groups
 */
export const getUserGroups = async (): Promise<User['groups']> => {
  try {
    if (!authToken.value || !isAuthenticated.value) {
      return [];
    }

    try {
      const response = await fetch(`${API_BASE_URL}/user/groups`, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': `Bearer ${authToken.value}`,
        },
      });

      if (!response.ok) {
        console.warn('Failed to get user groups, API returned:', response.status);
        return [];
      }

      const data = await response.json();

      // Update current user data with groups
      if (currentUser.value) {
        currentUser.value.groups = data.data;
        localStorage.setItem('user', JSON.stringify(currentUser.value));
      }

      return data.data;
    } catch (fetchError) {
      console.warn('Error fetching user groups, API might not be ready:', fetchError);
      return [];
    }
  } catch (error) {
    console.error('Error in getUserGroups:', error);
    return [];
  }
};

// Authentication service object
const authService = {
  register,
  login,
  logout,
  getCurrentUser,
  getUserGroups,
  enterGuestMode,
  isAuthenticated,
  isGuestMode,
  currentUser,
  authToken,
  authError,
};

export default authService;
