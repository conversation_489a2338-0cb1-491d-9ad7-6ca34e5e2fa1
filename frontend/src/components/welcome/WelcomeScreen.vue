<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { login as authLogin, register as authRegister, enterGuestMode } from '@/services/auth'
import apiService from '@/services/api'

interface Props {
  initialScreen?: 'welcome' | 'video' | 'login' | 'register'
}

const props = withDefaults(defineProps<Props>(), {
  initialScreen: 'welcome'
})

const router = useRouter()
const currentScreen = ref(props.initialScreen) // 'welcome', 'video', 'login', 'register'
const isTransitioning = ref(false)
const isVideoPlaying = ref(false)
const username = ref('')
const password = ref('')
const name = ref('')
const email = ref('')
const passwordConfirmation = ref('')
const isLoading = ref(false)
const loginError = ref('')
const registerError = ref('')

// Welcome screen settings
const welcomeSettings = ref({
  welcome_title: 'Bienvenido | Cork Experience',
  video_title: '',
  video_description: 'Los paisajes del corcho son los paisajes de Quercus suber, una especie endémica de la cuenca mediterránea. La mitad de la superficie mundial de alcornocal se encuentra en la Península Ibérica, sobre todo en su zona suroccidental, una buena parte al norte de África, y el resto se reparte entre el sur de Francia, el litoral oeste de Italia y las islas de Córcega, Cerdeña y Sicilia.\n\nLa diversidad del paisaje corchero es uno de sus bienes más preciados. No nos debe extrañar si tenemos en cuenta que su área de distribución tiene características climáticas muy distintas, así como la intensa y antigua relación del hombre con este ecosistema.',
  welcome_logo: '/assets/logos/CorkExpLogoInicial.png',
  video_logo: '/assets/logos/CorkExpLogo.png',
  video_placeholder: '/assets/images/videoplaceholder.jpeg',
  welcome_video: null
})
const isLoadingSettings = ref(false)

// Debug flag - set to true to disable automatic transitions
const debugMode = false

// Transition timing
const transitionDuration = 1500 // in milliseconds
const welcomeScreenDuration = 1000 // in milliseconds

onMounted(async () => {
  // Log initial default settings
  console.log('Initial welcome settings:', welcomeSettings.value)

  // Fetch welcome screen settings
  try {
    isLoadingSettings.value = true

    // Try to load settings from API if available
    try {
      // @ts-ignore - Ignore TypeScript error for settings property
      const response = await apiService.settings?.getWelcomeSettings()

      if (response && response.data) {
        // Get the API data
        const apiData = response.data;

        // Update settings with values from API
        welcomeSettings.value = {
          ...welcomeSettings.value,
          ...apiData
        };

        console.log('Welcome settings loaded from API:', welcomeSettings.value);
      }
    } catch (apiError) {
      console.warn('API settings endpoint not available or failed:', apiError)
      // Continue with default settings
    }
  } catch (error) {
    console.error('Error in welcome screen initialization:', error)
    // Continue with default settings if anything fails
  } finally {
    isLoadingSettings.value = false
  }

  // Only auto-transition if starting from welcome screen
  if (props.initialScreen === 'welcome' && !debugMode) {
    setTimeout(() => {
      console.log('Transitioning to video screen after delay')
      transitionToScreen('video')
    }, welcomeScreenDuration)
  } else if (props.initialScreen === 'welcome') {
    console.log('Debug mode enabled - automatic transitions disabled')
  } else {
    console.log(`Starting directly on ${props.initialScreen} screen`)
  }
})

const transitionToScreen = (targetScreen: 'welcome' | 'video' | 'login' | 'register') => {
  if (isTransitioning.value) return

  isTransitioning.value = true

  // After the transition animation completes, update the current screen
  setTimeout(() => {
    currentScreen.value = targetScreen
    isTransitioning.value = false
  }, transitionDuration)
}

const toggleVideo = () => {
  isVideoPlaying.value = !isVideoPlaying.value
}

const skipVideo = () => {
  transitionToScreen('login')
}

const continueAsGuest = () => {
  // Enter guest mode and navigate to home
  enterGuestMode()
  router.push('/inicio')
}

const login = async () => {
  if (!username.value || !password.value) {
    loginError.value = 'Por favor, introduce tu nombre de usuario y contraseña';
    return;
  }

  try {
    isLoading.value = true;
    loginError.value = '';

    console.log('Login attempt in WelcomeScreen with:', { username: username.value });

    // Call the authentication service to login
    // We're using the username field for either username or email
    await authLogin(username.value, password.value);

    console.log('Login successful, navigating to home');

    // Check for post-login redirect
    const redirectPath = localStorage.getItem('post_login_redirect');
    if (redirectPath) {
      localStorage.removeItem('post_login_redirect');
      router.push(redirectPath);
    } else {
      // If successful, navigate to home
      router.push('/inicio');
    }
  } catch (error) {
    console.error('Login error in WelcomeScreen:', error);

    if (error instanceof Error) {
      loginError.value = error.message;
    } else {
      loginError.value = 'Error al iniciar sesión. Por favor, inténtalo de nuevo.';
    }
  } finally {
    isLoading.value = false;
  }
}

const register = () => {
  // Switch to registration screen
  transitionToScreen('register');
}

const submitRegistration = async () => {
  if (!name.value || !email.value || !password.value || !passwordConfirmation.value) {
    registerError.value = 'Por favor, completa todos los campos';
    return;
  }

  if (password.value !== passwordConfirmation.value) {
    registerError.value = 'Las contraseñas no coinciden';
    return;
  }

  try {
    isLoading.value = true;
    registerError.value = '';

    // Call the authentication service to register
    await authRegister(name.value, email.value, password.value, passwordConfirmation.value);

    // Check for post-login redirect
    const redirectPath = localStorage.getItem('post_login_redirect');
    if (redirectPath) {
      localStorage.removeItem('post_login_redirect');
      router.push(redirectPath);
    } else {
      // If successful, navigate to home
      router.push('/inicio');
    }
  } catch (error) {
    if (error instanceof Error) {
      registerError.value = error.message;
    } else {
      registerError.value = 'Error al registrarse. Por favor, inténtalo de nuevo.';
    }
    console.error('Registration error:', error);
  } finally {
    isLoading.value = false;
  }
}

const backToLogin = () => {
  transitionToScreen('login');
}

const loginWithGoogle = () => {
  // This would typically handle Google OAuth authentication
  console.log('Login with Google clicked')
  // For now, we'll just show an alert
  alert('Google login will be implemented in the future')
}

const loginWithApple = () => {
  // This would typically handle Apple OAuth authentication
  console.log('Login with Apple clicked')
  // For now, we'll just show an alert
  alert('Apple login will be implemented in the future')
}

// Image load handler
const handleImageLoad = (imageType: string) => {
  console.log(`Successfully loaded image: ${imageType}`)
}

const handleVideoError = (event: Event) => {
  console.error('Failed to load video:', {
    src: welcomeSettings.value.welcome_video,
    error: (event.target as HTMLVideoElement)?.error
  })
  // If video fails to load, hide the video player
  isVideoPlaying.value = false
}


</script>

<template>
  <div class="welcome-screen-container">
    <!-- Welcome Screen -->
    <div
      class="screen welcome-screen"
      :class="{
        'active': currentScreen === 'welcome' && !isTransitioning,
        'fade-out': isTransitioning && currentScreen === 'welcome'
      }"
    >
      <div class="welcome-header">
        <h1 v-if="welcomeSettings.welcome_title">
          <span v-html="welcomeSettings.welcome_title.replace('|', '<strong>|</strong>')" />
        </h1>
        <h1 v-else><strong>Bienvenido |</strong> <span>Cork Experience</span></h1>
      </div>

      <div class="logo-container welcome-logo-container">
        <div class="logo-wrapper">
          <img :src="welcomeSettings.welcome_logo || '/assets/logos/CorkExpLogoInicial.png'" alt="Cork Experience" class="logo" @error="welcomeSettings.welcome_logo = '/assets/logos/CorkExpLogoInicial.png'" @load="handleImageLoad('welcome_logo')" />
        </div>
      </div>
    </div>

    <!-- Video Screen -->
    <div
      class="screen video-screen"
      :class="{
        'active': currentScreen === 'video' && !isTransitioning,
        'fade-in': isTransitioning && currentScreen === 'welcome',
        'fade-out': isTransitioning && currentScreen === 'video'
      }"
    >
    <div class="video-content">
      <div class="video-header">
        <img :src="welcomeSettings.video_logo || '/assets/logos/CorkExpLogo.png'" alt="Cork Experience" class="video-logo" @error="welcomeSettings.video_logo = '/assets/logos/CorkExpLogo.png'" @load="handleImageLoad('video_logo')" />
        <h2 v-if="welcomeSettings.video_title && welcomeSettings.video_title.trim()" class="video-title">{{ welcomeSettings.video_title }}</h2>
      </div>

      <div class="video-container" style="margin-top: 1.5rem; margin-bottom: 1.5rem;">
        <div class="video-placeholder" @click="toggleVideo">
          <img :src="welcomeSettings.video_placeholder || '/assets/images/videoplaceholder.jpeg'" alt="Cork Experience Video" class="placeholder-image" @error="welcomeSettings.video_placeholder = '/assets/images/videoplaceholder.jpeg'" @load="handleImageLoad('video_placeholder')" />
          <div v-if="!isVideoPlaying" class="play-button">
            <div class="play-icon"></div>
          </div>
          <video v-if="isVideoPlaying && welcomeSettings.welcome_video"
                 :src="welcomeSettings.welcome_video"
                 controls
                 autoplay
                 @error="handleVideoError"
                 class="video-player">
            <source :src="welcomeSettings.welcome_video" type="video/mp4">
            <p>Your browser doesn't support HTML5 video. Here is a <a :href="welcomeSettings.welcome_video">link to the video</a> instead.</p>
          </video>
        </div>
      </div>

      <div class="description" style="margin-top: 1.5rem; margin-bottom: 1.5rem;">
        <div v-if="welcomeSettings.video_description && welcomeSettings.video_description.trim()" v-html="welcomeSettings.video_description.replace(/\n/g, '<br>')" />
        <template v-else>
          <p>
            Los paisajes del corcho son los paisajes de Quercus suber, una especie endémica de la cuenca
            mediterránea. La mitad de la superficie mundial de alcornocal se encuentra en la Península Ibérica, sobre
            todo en su zona suroccidental, una buena parte al norte de África, y el resto se reparte entre el sur de
            Francia, el litoral oeste de Italia y las islas de Córcega, Cerdeña y Sicilia.
          </p>
          <p>
            La diversidad del paisaje corchero es uno de sus bienes más preciados. No nos debe extrañar si
            tenemos en cuenta que su área de distribución tiene características climáticas muy distintas, así
            como la intensa y antigua relación del hombre con este ecosistema.
          </p>
        </template>
      </div>

      <div class="skip-button-container">
        <button class="skip-button" @click="skipVideo">
          <strong>Saltar el video</strong>
          <span class="skip-icon">
            <i class="pi pi-step-forward"></i>
          </span>
        </button>
      </div>
    </div>
  </div>

  <!-- Login Screen -->
  <div
    class="screen login-screen"
    :class="{
      'active': currentScreen === 'login' && !isTransitioning,
      'fade-in': isTransitioning && currentScreen === 'video',
      'fade-out': isTransitioning && currentScreen === 'login'
    }"
  >
    <div class="login-content">
      <div class="login-logo-container">
        <img src="/assets/logos/CorkExpLogo.png" alt="Cork Experience" class="login-logo" />
        <p class="tagline">El corcho, un mundo por descubrir</p>
      </div>

      <form @submit.prevent="login" class="login-form">
        <div v-if="loginError" class="error-message">
          {{ loginError }}
        </div>

        <div class="input-group">
          <input
            type="text"
            v-model="username"
            placeholder="Usuario o Email"
            class="username-input"
            required
          />
        </div>

        <div class="input-group">
          <input
            type="password"
            v-model="password"
            placeholder="Contraseña"
            class="password-input"
            required
          />
        </div>

        <button type="submit" class="login-button" :disabled="isLoading">
          {{ isLoading ? 'Cargando...' : 'Iniciar sesión' }}
        </button>

        <button type="button" class="register-button" @click="register">
          Registrarse
        </button>

        <button type="button" class="guest-button" @click="continueAsGuest">
          Continuar como invitado
        </button>

        <div class="divider">
          <span class="divider-text">o</span>
        </div>

        <div class="social-login">
          <button type="button" class="social-button google-button" @click="loginWithGoogle">
            <i class="pi pi-google"></i>
            <span>Continuar con Google</span>
          </button>

          <button type="button" class="social-button apple-button" @click="loginWithApple">
            <i class="pi pi-apple"></i>
            <span>Continuar con Apple</span>
          </button>
        </div>
      </form>

      <p class="terms-text">
        Por favor <span class="highlight">revise su bandeja de entrada</span> de correo electrónico<br>para encontrar su nombre de usuario
      </p>
    </div>
  </div>

  <!-- Registration Screen -->
  <div
    class="screen login-screen registration-screen"
    :class="{
      'active': currentScreen === 'register' && !isTransitioning,
      'fade-in': isTransitioning && currentScreen === 'login',
      'fade-out': isTransitioning && currentScreen === 'register'
    }"
  >
    <div class="login-content">
      <div class="login-logo-container">
        <img src="/assets/logos/CorkExpLogo.png" alt="Cork Experience" class="login-logo" />
        <p class="tagline">Crear una cuenta nueva</p>
      </div>

      <form @submit.prevent="submitRegistration" class="login-form">
        <div v-if="registerError" class="error-message">
          {{ registerError }}
        </div>

        <div class="input-group">
          <input
            type="text"
            v-model="name"
            placeholder="Nombre completo"
            class="username-input"
            required
          />
        </div>

        <div class="input-group">
          <input
            type="email"
            v-model="email"
            placeholder="Correo electrónico"
            class="username-input"
            required
          />
        </div>

        <div class="input-group">
          <input
            type="password"
            v-model="password"
            placeholder="Contraseña"
            class="password-input"
            required
          />
        </div>

        <div class="input-group">
          <input
            type="password"
            v-model="passwordConfirmation"
            placeholder="Confirmar contraseña"
            class="password-input"
            required
          />
        </div>

        <button type="submit" class="login-button" :disabled="isLoading">
          {{ isLoading ? 'Cargando...' : 'Registrarse' }}
        </button>

        <button type="button" class="register-button" @click="backToLogin">
          Volver al inicio de sesión
        </button>
      </form>

      <p class="terms-text">
        Al registrarte, aceptas nuestros <span class="highlight">Términos y Condiciones</span> y nuestra <span class="highlight">Política de Privacidad</span>
      </p>
    </div>
  </div>
</div>
</template>

<style scoped>
/* Container for all screens */
.welcome-screen-container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

/* Common styles for all screens */
.screen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  transition: opacity 1.5s ease-in-out, visibility 0s;
  opacity: 0;
  visibility: hidden;
  z-index: 1;
  overflow: hidden;
  box-sizing: border-box;
}

/* Active screen (fully visible) */
.active {
  opacity: 1;
  visibility: visible;
  z-index: 2;
}

/* Screen that's fading out */
.fade-out {
  opacity: 0;
  visibility: visible;
  transition: opacity 1.5s ease-in-out, visibility 0s 1.5s;
  pointer-events: none;
  z-index: 2;
}

/* Screen that's fading in */
.fade-in {
  opacity: 1;
  visibility: visible;
  z-index: 3;
}

/* Welcome Screen Styles */
.welcome-screen {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background: linear-gradient(to bottom, #EEAE8F, #DC8960, #9A522E);
  color: #333;
  padding: 2rem;
  box-sizing: border-box;
  max-width: 100vw;
}

.welcome-header {
  text-align: center;
  padding-top: 1.5rem;
  padding-bottom: 0.5rem;
}

.welcome-header h1 {
  font-size: 1.2rem;
  font-weight: 500;
  color: #333;
}

.welcome-header span {
  font-weight: 400;
}

.welcome-logo-container {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-end;
  flex-grow: 1;
  margin: 0 -2rem -2rem -2rem;
  width: calc(100% + 4rem);
}

.logo-wrapper {
  background-color: white;
  padding: 2rem 1.5rem 2rem;
  border-radius: 12px 12px 0 0;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.logo {
  max-width: 90%;
  height: auto;
  margin-bottom: 0.5rem;
  max-height: 120px;
}

/* Video Screen Styles */
.video-screen {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  color: white;
  padding: 0;
  overflow-y: auto; /* Allow scrolling if needed */
  box-sizing: border-box;
  width: 100vw;
  height: 100vh;
  margin: 0;
  background-color: black;
}

.video-screen::before {
  content: '';
  position: absolute;
  top: -20px; /* Extend beyond the edges to prevent white borders during blur */
  left: -20px;
  right: -20px;
  bottom: -20px;
  background-image: url('/assets/images/videoplaceholder.jpeg');
  background-size: cover;
  background-position: center;
  filter: blur(15px) brightness(0.25);
  z-index: -1;
}

.video-content {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 450px;
  padding: 0 1rem;
  margin: 0 auto;
}

.video-header {
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
}

.video-logo {
  max-height: 60px;
  filter: brightness(1.2); /* Make logo brighter against dark background */
}

.video-title {
  color: white;
  font-size: 1.2rem;
  margin-top: 0.5rem;
  text-align: center;
  font-weight: 500;
}

.video-player {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
  z-index: 10;
}

@media (max-height: 700px) {
  .video-header {
    margin-bottom: 0.5rem;
  }

  .video-logo {
    max-height: 60px;
  }
}

.video-container {
  width: 100%;
  margin: 0 0 1rem 0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
  aspect-ratio: 16/9;
}

.video-placeholder {
  position: relative;
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
}

.placeholder-image {
  width: 100%;
  display: block;
  border-radius: 8px;
  object-fit: cover;
  aspect-ratio: 16/9;
  opacity: 0.9;
  transition: opacity 0.3s ease;
}

.video-placeholder:hover .placeholder-image {
  opacity: 1;
}

.play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.play-button:hover {
  background-color: rgba(255, 255, 255, 1);
  transform: translate(-50%, -50%) scale(1.05);
}

.play-icon {
  width: 0;
  height: 0;
  border-top: 12px solid transparent;
  border-left: 20px solid #DC8960;
  border-bottom: 12px solid transparent;
  margin-left: 5px;
}

.description {
  width: 100%;
  margin: 0;
  font-size: 0.8rem;
  line-height: 1.25;
  text-align: left;
  color: rgba(255, 255, 255, 0.95);
  font-family: 'Poppins', sans-serif;
  letter-spacing: 0.01em;
}

.description p {
  margin-bottom: 0.4rem;
}

.skip-button-container {
  display: flex;
  justify-content: center;
  margin-top: 0.8rem;
}

.skip-button {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.8);
  font-size: 1rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.4rem 0.8rem;
  transition: all 0.3s ease;
  font-weight: 600;
  font-family: 'Poppins', sans-serif;
  letter-spacing: 0.03em;
}

.skip-button:hover {
  color: white;
}

.skip-icon {
  margin-left: 0.4rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: inherit;
  vertical-align: middle;
}

.skip-icon i {
  font-size: 0.9rem;
}

/* Login Screen Styles */
.login-screen {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  box-sizing: border-box;
  max-width: 100vw;
  min-height: 100vh;
  overflow-y: auto;
  background-image:
    linear-gradient(to bottom,
      rgba(238, 174, 143, 0.5) 0%,
      rgba(220, 137, 96, 0.8) 40%,
      rgba(154, 82, 46, 0.95) 100%),
    url('/assets/backgrounds/backgroundLogin.png');
  background-size: cover;
  background-position: center;
  background-color: #9A522E; /* Fallback color */
}

.login-content {
  width: 100%;
  max-width: 320px;
  padding: 1rem;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 1;
}

.login-logo-container {
  margin-bottom: 2rem;
}

.login-logo {
  max-width: 80%;
  max-height: 80px;
  margin-bottom: 0.5rem;
  filter: drop-shadow(0 2px 6px rgba(0, 0, 0, 0.4));
  opacity: 0.95;
}

.tagline {
  color: white;
  font-size: 1rem;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
  font-weight: 500;
}

.login-form {
  width: 100%;
  margin-bottom: 1rem;
}

.input-group {
  margin-bottom: 1.5rem;
}

.username-input, .password-input {
  width: 100%;
  padding: 0.75rem;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
  color: #333;
}

.username-input:focus, .password-input:focus {
  outline: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.error-message {
  background-color: rgba(220, 53, 69, 0.8);
  color: white;
  padding: 0.5rem;
  border-radius: 4px;
  margin-bottom: 1rem;
  font-size: 0.9rem;
  text-align: center;
}

.login-button {
  width: 100%;
  padding: 0.75rem;
  background-color: rgba(255, 255, 255, 0.9);
  color: #333;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  margin-top: 0.5rem;
}

.login-button:hover {
  background-color: rgba(255, 255, 255, 1);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
}

.register-button, .guest-button {
  width: 100%;
  padding: 0.75rem;
  background-color: transparent;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.7);
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 0.75rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.register-button:hover, .guest-button:hover {
  background-color: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.guest-button {
  background-color: rgba(255, 255, 255, 0.1);
  margin-top: 0.5rem;
}

.divider {
  display: flex;
  align-items: center;
  margin: 1.5rem 0;
  width: 100%;
  position: relative;
}

.divider::before, .divider::after {
  content: '';
  flex: 1;
  height: 1px;
  background-color: rgba(255, 255, 255, 0.4);
}

.divider-text {
  padding: 0 0.75rem;
  color: white;
  font-size: 0.9rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.social-login {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.social-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.social-button i {
  margin-right: 0.5rem;
  font-size: 1.1rem;
}

.google-button {
  background-color: white;
  color: #444;
}

.google-button:hover {
  background-color: #f8f8f8;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.apple-button {
  background-color: black;
  color: white;
}

.apple-button:hover {
  background-color: #222;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.terms-text {
  font-size: 0.75rem;
  color: white;
  line-height: 1.5;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
  margin-top: 1.5rem;
  max-width: 90%;
}

.highlight {
  font-weight: bold;
}

/* Responsive styles */
@media (min-width: 768px) {
  .welcome-header h1 {
    font-size: 1.5rem;
  }

  .logo-wrapper {
    padding: 2.5rem 2rem 2rem;
  }

  .logo {
    max-width: 85%;
    max-height: 150px;
  }

  .video-logo {
    max-height: 80px;
  }

  .description {
    font-size: 1rem;
  }

  .login-content {
    padding: 2rem;
    max-width: 360px;
  }

  .login-logo {
    max-height: 100px;
  }

  .terms-text {
    font-size: 0.8rem;
    margin-top: 2.5rem;
  }

  .social-button {
    padding: 0.85rem;
    font-size: 1rem;
  }

  .social-button i {
    font-size: 1.2rem;
  }

  .divider {
    margin: 1.75rem 0;
  }

  .divider-text {
    font-size: 1rem;
  }
}
</style>
