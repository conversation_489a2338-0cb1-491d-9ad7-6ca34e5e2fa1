<template>
  <header class="app-header">
    <div class="header-left">
      <button v-if="showProfileButton" class="icon-button profile-button" @click="navigateToProfile">
        <i class="pi pi-user"></i>
      </button>
      <slot name="left"></slot>
    </div>

    <div v-if="showLogo" class="logo-container">
      <img :src="logoSrc" alt="Cork Experience" class="header-logo" />
    </div>
    <div v-else class="title-container" :class="{ 'left-aligned': leftAlignedTitle }">
      <h1 class="view-title">{{ title }}</h1>
      <slot name="title"></slot>
    </div>

    <div class="header-actions">
      <div class="action-group">
        <button v-if="showFavoriteButton" class="icon-button favorite-button">
          <i class="pi pi-heart"></i>
        </button>
        <button v-if="showCallButton" class="icon-button call-button">
          <i class="pi pi-phone"></i>
        </button>
        <button v-if="showShareButton" class="icon-button share-button">
          <i class="pi pi-share-alt"></i>
        </button>
        <button v-if="showMenuButton" class="icon-button menu-button" @click="$emit('toggleMenu')">
          <i class="pi pi-bars"></i>
        </button>
        <button v-if="showBackButton" class="back-button" @click="goBack">
          <i class="pi pi-arrow-left"></i>
        </button>
        <slot name="actions"></slot>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';

defineProps({
  title: {
    type: String,
    default: ''
  },
  leftAlignedTitle: {
    type: Boolean,
    default: true
  },
  showBackButton: {
    type: Boolean,
    default: false
  },
  showFavoriteButton: {
    type: Boolean,
    default: false
  },
  showCallButton: {
    type: Boolean,
    default: false
  },
  showShareButton: {
    type: Boolean,
    default: false
  },
  showProfileButton: {
    type: Boolean,
    default: false
  },
  showMenuButton: {
    type: Boolean,
    default: false
  },
  showLogo: {
    type: Boolean,
    default: false
  },
  logoSrc: {
    type: String,
    default: '/assets/logos/CorkExpLogoBlack.png'
  }
});

const emit = defineEmits(['toggleMenu', 'goBack']);

const router = useRouter();

const goBack = () => {
  emit('goBack');
};

const navigateToProfile = () => {
  router.push('/perfil');
};
</script>

<style scoped>
.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.25rem;
  padding-top: 2rem; /* Extra padding at the top for device notches/status bars */
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100; /* Higher z-index to ensure it's above all other elements */
  height: 90px; /* Increased height to accommodate extra top padding */
}

.header-left {
  width: 20px; /* Further reduced width to make more room for left-aligned title */
  display: flex;
  justify-content: flex-start;
  padding-top: 0.5rem; /* Adjust vertical position to account for extra top padding */
}

.header-actions {
  width: 120px; /* Increased width to accommodate menu button */
  display: flex;
  justify-content: flex-end;
  padding-top: 0.5rem; /* Adjust vertical position to account for extra top padding */
}

.action-group {
  display: flex;
  gap: 0.5rem; /* Reduced gap between buttons */
  position: relative;
  z-index: 101; /* Higher than the header to ensure buttons are clickable */
}

.title-container {
  flex: 1;
  text-align: left;
  position: relative;
  padding-left: 0; /* Removed padding to move title more to the left */
  margin-left: -20px; /* Increased negative margin to move title even more to the left */
  justify-content: flex-start;
  display: flex;
  width: auto;
  z-index: 1;
}

.title-container:not(.left-aligned) {
  text-align: center;
  position: absolute; /* Absolute positioning to ensure centering */
  left: 0;
  right: 0;
  margin: auto;
  padding-left: 0;
  justify-content: center;
  z-index: -1; /* Place behind other elements */
}

.view-title {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #334960;
  font-family: 'Poppins', sans-serif;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: left;
}

.title-container:not(.left-aligned) .view-title {
  text-align: center;
}

.logo-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute; /* Absolute positioning to ensure centering */
  left: 0;
  right: 0;
  margin: auto;
  z-index: -1; /* Place behind other elements */
  padding-top: 0.5rem; /* Adjust vertical position to account for extra top padding */
}

.header-logo {
  height: 50px;
  margin: 0;
  object-fit: contain;
}

.icon-button {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: #666;
  width: 40px; /* Slightly smaller */
  height: 40px; /* Slightly smaller */
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s;
}

.back-button {
  background: white;
  border: 1px solid #e0e0e0;
  font-size: 1.25rem;
  color: #597694;
  width: 40px; /* Match other buttons */
  height: 40px; /* Match other buttons */
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s;
  position: relative;
  z-index: 101; /* Higher than the header to ensure it's clickable */
}

.menu-button {
  background-color: #597694;
  color: white;
  width: 40px; /* Match other buttons */
  height: 40px; /* Match other buttons */
}

.profile-button {
  background-color: #f0f2f5;
  color: #597694;
}

.favorite-button {
  position: relative;
  top: 2px;
}

.favorite-button i {
  color: #597694;
  font-size: 1.8rem;
  position: relative;
}

.call-button {
  background-color: #f0f2f5;
  color: #597694;
}

.call-button i {
  color: #597694;
  font-size: 1.2rem;
}

.back-button:hover, .icon-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.menu-button:hover {
  background-color: #4a6380;
}

/* Header actions styles moved above */

.placeholder {
  width: 40px;
}
</style>
