<template>
  <div v-if="isVisible" class="login-modal-overlay" @click="closeModal">
    <div class="login-modal" @click.stop>
      <div class="modal-header">
        <h2>Iniciar Se<PERSON><PERSON></h2>
        <button class="close-button" @click="closeModal">
          <i class="pi pi-times"></i>
        </button>
      </div>

      <div class="modal-content">
        <p class="login-message">{{ message }}</p>

        <form @submit.prevent="handleLogin" class="login-form">
          <div class="form-group">
            <label for="email">Email</label>
            <input
              id="email"
              v-model="email"
              type="email"
              required
              placeholder="<EMAIL>"
              class="form-input"
            />
          </div>

          <div class="form-group">
            <label for="password">Contraseña</label>
            <input
              id="password"
              v-model="password"
              type="password"
              required
              placeholder="Tu contraseña"
              class="form-input"
            />
          </div>

          <div v-if="error" class="error-message">
            {{ error }}
          </div>

          <button type="submit" :disabled="isLoading" class="login-submit-button">
            <i v-if="isLoading" class="pi pi-spinner pi-spin"></i>
            <span v-else>Iniciar Sesión</span>
          </button>
        </form>

        <div class="modal-footer">
          <p>¿No tienes cuenta? <a href="#" @click="handleRegister">Regístrate aquí</a></p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { login } from '@/services/auth';

interface Props {
  isVisible: boolean;
  message?: string;
}

const props = withDefaults(defineProps<Props>(), {
  message: 'Para acceder a esta función necesitas iniciar sesión'
});

const emit = defineEmits(['close', 'loginSuccess']);

const router = useRouter();
const email = ref('');
const password = ref('');
const error = ref('');
const isLoading = ref(false);

const closeModal = () => {
  emit('close');
  // Reset form
  email.value = '';
  password.value = '';
  error.value = '';
};

const handleLogin = async () => {
  try {
    isLoading.value = true;
    error.value = '';

    await login(email.value, password.value);
    
    emit('loginSuccess');
    closeModal();

    // Check for post-login redirect
    const redirectPath = localStorage.getItem('post_login_redirect');
    if (redirectPath) {
      localStorage.removeItem('post_login_redirect');
      router.push(redirectPath);
    }
  } catch (err: any) {
    error.value = err.message || 'Error al iniciar sesión';
  } finally {
    isLoading.value = false;
  }
};

const handleRegister = () => {
  // For now, just close modal and redirect to registration
  closeModal();
  router.push('/registro');
};
</script>

<style scoped>
.login-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.login-modal {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 400px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 1.5rem 0;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 1.5rem;
}

.modal-header h2 {
  margin: 0;
  color: #333;
  font-size: 1.3rem;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  font-size: 1.2rem;
  color: #666;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: background-color 0.3s;
}

.close-button:hover {
  background-color: #f0f0f0;
}

.modal-content {
  padding: 0 1.5rem 1.5rem;
}

.login-message {
  color: #666;
  margin-bottom: 1.5rem;
  text-align: center;
  font-size: 0.9rem;
  line-height: 1.4;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 500;
  color: #333;
  font-size: 0.9rem;
}

.form-input {
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s;
}

.form-input:focus {
  outline: none;
  border-color: #597694;
  box-shadow: 0 0 0 2px rgba(89, 118, 148, 0.1);
}

.error-message {
  color: #e74c3c;
  font-size: 0.85rem;
  text-align: center;
  padding: 0.5rem;
  background-color: #fdf2f2;
  border-radius: 6px;
  border: 1px solid #fecaca;
}

.login-submit-button {
  background: linear-gradient(135deg, #597694 0%, #4a6380 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.75rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 0.5rem;
}

.login-submit-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #4a6380 0%, #3d5269 100%);
  transform: translateY(-1px);
}

.login-submit-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.modal-footer {
  text-align: center;
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid #e0e0e0;
}

.modal-footer p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.modal-footer a {
  color: #597694;
  text-decoration: none;
  font-weight: 500;
}

.modal-footer a:hover {
  text-decoration: underline;
}
</style>
