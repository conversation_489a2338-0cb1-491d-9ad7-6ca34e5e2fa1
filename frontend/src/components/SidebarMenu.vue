<template>
  <div class="sidebar-overlay" v-if="isOpen" @click="close"></div>
  <div class="sidebar-menu" :class="{ 'open': isOpen }">
    <div class="sidebar-header">
      <img src="/assets/logos/CorkExpLogoBlack.png" alt="Cork Experience Logo" class="sidebar-logo">
    </div>
    <div class="menu-label">MENU</div>
    <nav class="sidebar-nav">
      <div class="menu-item-container">
        <router-link to="/home" class="menu-item" @click="close">
          <i class="pi pi-home" style="color: #DC8960;"></i>
          <span class="menu-text home">Inicio</span>
        </router-link>
        <div class="menu-divider"></div>
      </div>
      <div class="menu-item-container">
        <router-link to="/rutas" class="menu-item" @click="close">
          <img src="/assets/icons/MapaBlack.png" alt="Rutas" class="menu-icon">
          <span class="menu-text">Rutas</span>
        </router-link>
        <div class="menu-divider"></div>
      </div>
      <div class="menu-item-container">
        <router-link to="/agenda" class="menu-item" @click="close">
          <img src="/assets/icons/Calendar.png" alt="Agenda" class="menu-icon">
          <span class="menu-text">Agenda</span>
        </router-link>
        <div class="menu-divider"></div>
      </div>
      <div class="menu-item-container">
        <router-link to="/experiencias" class="menu-item" @click="close">
          <img src="/assets/icons/ExperienciasBlack.png" alt="Experiencias" class="menu-icon">
          <span class="menu-text">Experiencias</span>
        </router-link>
        <div class="menu-divider"></div>
      </div>
      <div class="menu-item-container">
        <router-link to="/mapa" class="menu-item" @click="close">
          <img src="/assets/icons/MapaBlack.png" alt="Mapa" class="menu-icon">
          <span class="menu-text">Mapa</span>
        </router-link>
        <div class="menu-divider"></div>
      </div>
      <div class="menu-item-container">
        <router-link to="/agencias" class="menu-item" @click="close">
          <img src="/assets/icons/Operadores.png" alt="Agencias" class="menu-icon">
          <span class="menu-text">Agencias</span>
        </router-link>
        <div class="menu-divider"></div>
      </div>


      <div class="menu-item-container">
        <router-link to="/perfil" class="menu-item" @click="close">
          <img src="/assets/icons/Perfil.png" alt="Perfil" class="menu-icon">
          <span class="menu-text">Perfil</span>
        </router-link>
        <div class="menu-divider"></div>
      </div>
      <div class="menu-item-container">
        <router-link to="/storytours" class="menu-item" @click="close">
          <img src="/assets/icons/VR.png" alt="StoryTours" class="menu-icon">
          <span class="menu-text">StoryTours</span>
        </router-link>
      </div>
      <!-- Guide Dashboard - Only visible to guides -->
      <div v-if="isGuide" class="menu-item-container">
        <router-link to="/guia" class="menu-item guide-item" @click="close">
          <i class="pi pi-cog"></i>
          <span class="menu-text guide">Panel de Guía</span>
        </router-link>
        <div class="menu-divider"></div>
      </div>
    </nav>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { isGuide as checkIsGuide } from '../services/auth';

interface Props {
  isOpen: boolean;
}

defineProps<Props>();

const emit = defineEmits<{
  close: [];
}>();

// Check if current user is a guide using the new role system
const isGuide = computed(() => {
  return checkIsGuide();
});

const close = (): void => {
  emit('close');
};
</script>

<style scoped>
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(3px);
  -webkit-backdrop-filter: blur(3px);
  z-index: 998;
}

.sidebar-menu {
  position: fixed;
  top: 0;
  left: -360px;
  width: 360px;
  height: 100%;
  background: linear-gradient(180deg, rgba(232, 235, 238, 0.95) 0%, rgba(197, 205, 214, 0.95) 83.81%, rgba(139, 156, 174, 0.95) 99.99%, rgba(108, 130, 153, 0.95) 100%);
  z-index: 999;
  transition: left 0.3s ease;
  display: flex;
  flex-direction: column;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.sidebar-menu.open {
  left: 0;
}

.sidebar-header {
  padding: 1.5rem 2.5rem 1rem;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.sidebar-logo {
  height: 70px;
  object-fit: contain;
  margin-top: 30px;
}

.menu-label {
  padding: 1rem 2.5rem 0.5rem;
  color: #8BA8C7;
  font-size: 0.75rem;
  letter-spacing: 1.5px;
  font-weight: 400;
}

.sidebar-nav {
  display: flex;
  flex-direction: column;
  padding: 0.25rem 0;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 0.9rem 2.5rem;
  color: #334960;
  text-decoration: none;
  transition: background-color 0.2s;
  width: 100%;
}

.menu-item:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.menu-item-container:last-child .menu-divider {
  display: none;
}

.menu-item.router-link-active {
  background-color: rgba(255, 255, 255, 0.2);
}

.menu-item i {
  font-size: 1.4rem;
  width: 28px;
  margin-right: 0.75rem;
  color: #334960;
}

.menu-icon {
  width: 28px;
  height: 28px;
  margin-right: 0.75rem;
  object-fit: contain;
}

.menu-text {
  font-size: 0.95rem;
  color: #334960;
  font-family: 'Poppins', sans-serif;
  font-weight: 400;
  margin-left: 0.25rem;
}

.menu-text.home {
  color: #DC8960;
  font-weight: 600;
}

.menu-text.guide {
  color: #2c5aa0;
  font-weight: 600;
}

.guide-item {
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);
  border-left: 4px solid #2c5aa0;
}

.guide-item:hover {
  background: linear-gradient(135deg, #e8f2ff 0%, #d4e9ff 100%);
}

.menu-item-container {
  display: flex;
  flex-direction: column;
}

.menu-divider {
  height: 1px;
  background: linear-gradient(to right, transparent, rgba(108, 130, 153, 0.25) 15%, rgba(108, 130, 153, 0.25) 85%, transparent);
  margin: 0 1rem;
}
</style>
