<template>
  <div class="routes-view">
    <!-- Sidebar Menu -->
    <SidebarMenu :is-open="isSidebarOpen" @close="closeSidebar" />

    <!-- Header -->
    <AppHeader
      :showLogo="true"
      logoSrc="/assets/logos/CorkExpLogoBlack.png"
      :showFavoriteButton="true"
      :showMenuButton="true"
      @toggleMenu="toggleSidebar"
    />

    <!-- Title -->
    <h1 class="routes-title">Rutas</h1>

    <!-- Search Bar -->
    <div class="search-container">
      <div class="search-bar">
        <input
          type="text"
          v-model="searchQuery"
          placeholder="Buscar"
          class="search-input"
        >
        <button class="search-button">
          <i class="pi pi-search"></i>
        </button>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="loading-container">
      <i class="pi pi-spin pi-spinner loading-icon"></i>
      <p>Cargando rutas...</p>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="error-container">
      <i class="pi pi-exclamation-triangle error-icon"></i>
      <p>{{ error }}</p>
      <button @click="fetchRoutes" class="retry-button">Reintentar</button>
    </div>

    <!-- Empty State -->
    <div v-else-if="routes.length === 0" class="empty-container">
      <i class="pi pi-map empty-icon"></i>
      <p>No hay rutas disponibles</p>
    </div>

    <!-- Routes List -->
    <div v-else class="routes-grid">
      <div
        v-for="route in filteredRoutes"
        :key="route.id"
        class="route-card"
        @click="navigateToRouteDetail(route.id)"
      >
        <div class="route-image-container">
          <img
            :src="route.image || '/assets/images/placeholder.jpg'"
            :alt="route.title"
            class="route-image"
          >
          <div v-if="route.is_featured" class="featured-badge">
            <i class="pi pi-star"></i>
          </div>
        </div>
        <div class="route-content">
          <h3 class="route-title">{{ route.title }}</h3>
          <p class="route-description">{{ route.short_description || truncateText(route.description, 100) }}</p>
          <div class="route-details">
            <div v-if="route.duration" class="route-detail">
              <i class="pi pi-clock"></i>
              <span>{{ route.duration }}</span>
            </div>
            <div v-if="route.distance" class="route-detail">
              <i class="pi pi-map-marker"></i>
              <span>{{ route.distance }}</span>
            </div>
            <div v-if="route.difficulty" class="route-detail">
              <i class="pi pi-chart-line"></i>
              <span>{{ route.difficulty }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import SidebarMenu from '../components/SidebarMenu.vue';
import AppHeader from '../components/layout/AppHeader.vue';
import apiService from '@/services/api';

const router = useRouter();
const routes = ref<any[]>([]);
const isLoading = ref(true);
const error = ref<string | null>(null);
const searchQuery = ref('');
const isSidebarOpen = ref(false);

// Computed property for filtered routes
const filteredRoutes = computed(() => {
  if (!searchQuery.value) return routes.value;

  const query = searchQuery.value.toLowerCase();
  return routes.value.filter(route =>
    route.title.toLowerCase().includes(query) ||
    (route.description && route.description.toLowerCase().includes(query))
  );
});

// Fetch routes from API
const fetchRoutes = async () => {
  isLoading.value = true;
  error.value = null;

  try {
    const response = await apiService.routes.getAll();
    routes.value = response.data;
  } catch (err) {
    console.error('Error fetching routes:', err);
    error.value = 'No se pudieron cargar las rutas. Por favor, inténtalo de nuevo.';
  } finally {
    isLoading.value = false;
  }
};

// Navigate to route detail
const navigateToRouteDetail = (id: number) => {
  router.push(`/rutas/${id}`);
};

// Sidebar toggle
const toggleSidebar = () => {
  isSidebarOpen.value = !isSidebarOpen.value;
};

const closeSidebar = () => {
  isSidebarOpen.value = false;
};

// Helper function to truncate text
const truncateText = (text: string, maxLength: number) => {
  if (!text) return '';
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

// Fetch routes on component mount
onMounted(() => {
  fetchRoutes();
});
</script>

<style scoped>
.routes-view {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 2rem;
}

.routes-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #334960;
  margin: 1rem 1.5rem 0.5rem;
  font-family: 'Poppins', sans-serif;
}

.search-container {
  padding: 0 1.5rem;
  margin-bottom: 1rem;
}

.search-bar {
  display: flex;
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.search-input {
  flex: 1;
  padding: 0.75rem 1rem;
  border: none;
  outline: none;
  font-family: 'Poppins', sans-serif;
}

.search-button {
  background-color: #DC8960;
  color: white;
  border: none;
  padding: 0 1rem;
  cursor: pointer;
}

.loading-container,
.error-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  color: #334960;
  height: 50vh;
}

.loading-icon,
.error-icon,
.empty-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: #DC8960;
}

.retry-button {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background-color: #DC8960;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.routes-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  padding: 0 1.5rem;
}

@media (min-width: 640px) {
  .routes-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .routes-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.route-card {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
  cursor: pointer;
}

.route-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.route-image-container {
  position: relative;
  height: 160px;
}

.route-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.featured-badge {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background-color: #DC8960;
  color: white;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.route-content {
  padding: 1rem;
}

.route-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #334960;
  margin-bottom: 0.5rem;
  font-family: 'Poppins', sans-serif;
}

.route-description {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.75rem;
  line-height: 1.4;
}

.route-details {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  font-size: 0.8rem;
  color: #334960;
}

.route-detail {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.route-detail i {
  color: #DC8960;
}
</style>
