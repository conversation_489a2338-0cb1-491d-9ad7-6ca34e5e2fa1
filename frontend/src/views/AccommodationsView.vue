<template>
  <div class="accommodations-view">
    <SidebarMenu :isOpen="isSidebarOpen" @close="closeSidebar" />
    <!-- Header -->
    <AppHeader
      title="Alojamiento"
      :showFavoriteButton="true"
      :showMenuButton="true"
      :showBackButton="true"
      @toggleMenu="toggleSidebar"
      @goBack="router.go(-1)"
    />

    <!-- Search Bar -->
    <div class="search-container">
      <div class="search-bar">
        <input
          type="text"
          v-model="searchQuery"
          placeholder="Buscar alojamientos..."
          class="search-input"
        >
        <button class="search-button">
          <i class="pi pi-search"></i>
        </button>
      </div>
    </div>

    <!-- Main Content -->
    <main class="main-content">
      <!-- Loading state -->
      <div v-if="isLoading" class="loading-container">
        <div class="loading-spinner"></div>
        <p>Cargando alojamientos...</p>
      </div>

      <!-- Error message -->
      <div v-else-if="error" class="error-container">
        <p class="error-message">{{ error }}</p>
        <button @click="fetchAccommodations" class="retry-button">Reintentar</button>
      </div>

      <!-- No results -->
      <div v-else-if="filteredAccommodations.length === 0" class="no-results">
        No se encontraron alojamientos
      </div>

      <!-- Accommodations list -->
      <div v-else class="accommodations-list">
        <div
          v-for="accommodation in filteredAccommodations"
          :key="accommodation.id"
          class="accommodation-card"
          @click="viewAccommodationDetails(accommodation)"
        >
          <div class="accommodation-image-container">
            <img :src="accommodation.image" :alt="accommodation.title" class="accommodation-image" />
            <div v-if="accommodation.distance" class="accommodation-distance">{{ accommodation.distance }}</div>
          </div>
          <div class="accommodation-details">
            <h3 class="accommodation-title">{{ accommodation.title }}</h3>
            <p class="accommodation-description">{{ accommodation.shortDescription }}</p>
            <div class="accommodation-info">
              <div v-if="accommodation.location" class="accommodation-location">
                <i class="pi pi-map-marker"></i>
                <span>{{ accommodation.location }}</span>
              </div>
              <div v-if="accommodation.price" class="accommodation-price">
                <i class="pi pi-euro"></i>
                <span>{{ formatPrice(accommodation.price) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import SidebarMenu from '../components/SidebarMenu.vue';
import AppHeader from '../components/layout/AppHeader.vue';
import apiService from '@/services/api';

const router = useRouter();
const isSidebarOpen = ref(false);
const accommodations = ref<any[]>([]);
const isLoading = ref(true);
const error = ref<string | null>(null);
const searchQuery = ref('');

const toggleSidebar = () => {
  isSidebarOpen.value = !isSidebarOpen.value;
};

const closeSidebar = () => {
  isSidebarOpen.value = false;
};

// Function to fetch accommodations from the API
const fetchAccommodations = async (): Promise<void> => {
  try {
    isLoading.value = true;
    error.value = null;

    // Use the API service to fetch accommodations (hotel-type experiences)
    const data = await apiService.accommodations.getAll();

    // Transform the API data to match our frontend structure
    accommodations.value = data.data.map((item: any) => ({
      id: item.id,
      title: item.title,
      shortDescription: item.short_description || '',
      description: item.description,
      location: item.location ? item.location.name : '',
      distance: item.distance || '',
      price: item.price,
      image: item.image ? (item.image.startsWith('/') ? item.image : `/storage/${item.image}`) : '/assets/images/experiences/hotelcasacormoran.jpeg',
      type: item.type,
      isFeatured: item.is_featured,
      isActive: item.is_active
    }));
  } catch (err) {
    console.error('Error fetching accommodations:', err);
    error.value = 'Error al cargar alojamientos. Por favor, inténtelo de nuevo más tarde.';

    // Fallback to sample data if API fails
    accommodations.value = [
      {
        id: 3,
        title: 'Hotel Casa Convento La Almoraima',
        shortDescription: 'Alojamiento sostenible construido con materiales de corcho, ofreciendo una experiencia única.',
        description: 'El Hotel Casa Convento La Almoraima es un alojamiento único que destaca por su arquitectura sostenible, utilizando el corcho como material principal en su construcción. Este innovador hotel ofrece una experiencia inmersiva donde podrás apreciar las propiedades aislantes, acústicas y térmicas del corcho mientras disfrutas de una estancia confortable.',
        location: 'Ctra Algeciras-ronda, s/n, 11350 Castellar de la Frontera, Cádiz',
        distance: '8 km',
        price: 85,
        image: '/assets/images/experiences/hotelcasacormoran.jpeg',
        type: 'hotel',
        isFeatured: true,
        isActive: true
      }
    ];
  } finally {
    isLoading.value = false;
  }
};

// Computed property for filtered accommodations based on search query
const filteredAccommodations = computed(() => {
  if (!searchQuery.value) return accommodations.value;

  const query = searchQuery.value.toLowerCase();
  return accommodations.value.filter(accommodation =>
    accommodation.title.toLowerCase().includes(query) ||
    (accommodation.shortDescription && accommodation.shortDescription.toLowerCase().includes(query)) ||
    (accommodation.location && accommodation.location.toLowerCase().includes(query))
  );
});

// Format price
const formatPrice = (price: number): string => {
  return new Intl.NumberFormat('es-ES', { style: 'currency', currency: 'EUR' }).format(price);
};

// Navigate to accommodation details
const viewAccommodationDetails = (accommodation: any) => {
  router.push(`/experiencias/${accommodation.id}`);
};

// Fetch accommodations when component is mounted
onMounted(() => {
  fetchAccommodations();
});
</script>

<style scoped>
.accommodations-view {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.search-container {
  padding: 1rem;
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: #f5f5f5;
}

.search-bar {
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 8px;
  padding: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-input {
  flex: 1;
  border: none;
  padding: 0.5rem;
  font-size: 1rem;
  outline: none;
}

.search-button {
  background: none;
  border: none;
  padding: 0.5rem;
  cursor: pointer;
  color: #334960;
}

.main-content {
  flex: 1;
  padding: 0 1rem 1rem;
  overflow-y: auto;
}

.accommodations-list {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.accommodation-card {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.accommodation-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.accommodation-image-container {
  height: 180px;
  position: relative;
}

.accommodation-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.accommodation-distance {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background-color: rgba(51, 73, 96, 0.8);
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
}

.accommodation-details {
  padding: 1rem;
}

.accommodation-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #334960;
  margin-bottom: 0.5rem;
}

.accommodation-description {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.75rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.accommodation-info {
  display: flex;
  justify-content: space-between;
  font-size: 0.85rem;
  color: #888;
}

.accommodation-location, .accommodation-price {
  display: flex;
  align-items: center;
}

.accommodation-location i, .accommodation-price i {
  margin-right: 0.25rem;
}

/* Loading state styles */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #334960;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Error state styles */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

.error-message {
  color: #e74c3c;
  margin-bottom: 1rem;
}

.retry-button {
  background-color: #334960;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.retry-button:hover {
  background-color: #263a4d;
}

.no-results {
  text-align: center;
  padding: 2rem;
  color: #666;
  font-style: italic;
}

/* Responsive Styles */
@media (min-width: 640px) {
  .accommodations-list {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .accommodations-list {
    grid-template-columns: repeat(3, 1fr);
  }
}
</style>
