<template>
  <div class="login-view">
    <!-- Header -->
    <AppHeader
      :showBackButton="true"
      :showLogo="true"
      logoSrc="/assets/logos/CorkExpLogoBlack.png"
      @goBack="handleGoBack"
    />

    <!-- Main Content -->
    <main class="main-content">
      <div class="login-container">
        <div class="welcome-section">
          <h1>¡Bienvenido de vuelta!</h1>
          <p>Inicia sesión para acceder a todas las funcionalidades de Cork Experience</p>
        </div>

        <form @submit.prevent="handleLogin" class="login-form">
          <div class="form-group">
            <label for="email">Email</label>
            <input
              id="email"
              v-model="email"
              type="email"
              required
              placeholder="<EMAIL>"
              class="form-input"
              :disabled="isLoading"
            />
          </div>

          <div class="form-group">
            <label for="password">Contraseña</label>
            <input
              id="password"
              v-model="password"
              type="password"
              required
              placeholder="Tu contraseña"
              class="form-input"
              :disabled="isLoading"
            />
          </div>

          <div v-if="error" class="error-message">
            {{ error }}
          </div>

          <button type="submit" :disabled="isLoading" class="login-button">
            <i v-if="isLoading" class="pi pi-spinner pi-spin"></i>
            <span v-else>Iniciar Sesión</span>
          </button>
        </form>

        <div class="auth-footer">
          <p>¿No tienes cuenta? <router-link to="/registro" class="auth-link">Regístrate aquí</router-link></p>
          <p><router-link to="/inicio" class="auth-link">Continuar como invitado</router-link></p>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { login } from '@/services/auth';
import AppHeader from '@/components/layout/AppHeader.vue';

const router = useRouter();
const email = ref('');
const password = ref('');
const error = ref('');
const isLoading = ref(false);

const handleGoBack = () => {
  router.back();
};

const handleLogin = async () => {
  try {
    isLoading.value = true;
    error.value = '';

    await login(email.value, password.value);
    
    // Check for post-login redirect
    const redirectPath = localStorage.getItem('post_login_redirect');
    if (redirectPath) {
      localStorage.removeItem('post_login_redirect');
      router.push(redirectPath);
    } else {
      // Default redirect to home
      router.push('/inicio');
    }
  } catch (err: any) {
    error.value = err.message || 'Error al iniciar sesión';
  } finally {
    isLoading.value = false;
  }
};
</script>

<style scoped>
.login-view {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
}

.login-container {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
}

.welcome-section {
  text-align: center;
  margin-bottom: 2rem;
}

.welcome-section h1 {
  color: #333;
  font-size: 1.5rem;
  margin: 0 0 0.5rem 0;
  font-weight: 600;
}

.welcome-section p {
  color: #666;
  font-size: 0.9rem;
  margin: 0;
  line-height: 1.4;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 500;
  color: #333;
  font-size: 0.9rem;
}

.form-input {
  padding: 0.875rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s, box-shadow 0.3s;
}

.form-input:focus {
  outline: none;
  border-color: #597694;
  box-shadow: 0 0 0 3px rgba(89, 118, 148, 0.1);
}

.form-input:disabled {
  background-color: #f8f9fa;
  cursor: not-allowed;
}

.error-message {
  color: #e74c3c;
  font-size: 0.85rem;
  text-align: center;
  padding: 0.75rem;
  background-color: #fdf2f2;
  border-radius: 6px;
  border: 1px solid #fecaca;
}

.login-button {
  background: linear-gradient(135deg, #597694 0%, #4a6380 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.875rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.login-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #4a6380 0%, #3d5269 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(89, 118, 148, 0.3);
}

.login-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.auth-footer {
  text-align: center;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e0e0e0;
}

.auth-footer p {
  margin: 0.5rem 0;
  color: #666;
  font-size: 0.9rem;
}

.auth-link {
  color: #597694;
  text-decoration: none;
  font-weight: 500;
}

.auth-link:hover {
  text-decoration: underline;
}

/* Responsive */
@media (max-width: 480px) {
  .main-content {
    padding: 1rem;
  }
  
  .login-container {
    padding: 1.5rem;
  }
  
  .welcome-section h1 {
    font-size: 1.3rem;
  }
}
</style>
