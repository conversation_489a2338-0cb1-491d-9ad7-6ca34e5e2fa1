<template>
  <div class="agencies-view">
    <!-- Sidebar Menu (reused from HomeView) -->
    <SidebarMenu :is-open="isSidebarOpen" @close="closeSidebar" />

    <!-- Header -->
    <AppHeader
      title="Agencias"
      :showFavoriteButton="true"
      :showMenuButton="true"
      @toggleMenu="toggleSidebar"
    />

    <!-- Loading state -->
    <div v-if="isLoading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>Cargando agencias...</p>
    </div>

    <!-- Error message -->
    <div v-else-if="error" class="error-container">
      <p class="error-message">{{ error }}</p>
      <button @click="fetchAgencies" class="retry-button">Reintentar</button>
    </div>

    <!-- Agencies List -->
    <div v-else class="agencies-list">
      <div v-if="filteredAgencies.length === 0" class="no-results">
        No se encontraron agencias
      </div>
      <div v-else class="agencies-grid">
        <div
          v-for="agency in filteredAgencies"
          :key="agency.id"
          class="agency-card"
          @click="viewAgencyDetails(agency)"
        >
          <div class="agency-logo-container">
            <img v-if="agency.logo" :src="agency.logo" :alt="agency.name" class="agency-logo" />
            <div v-else class="agency-logo-placeholder">
              {{ agency.name.charAt(0) }}
            </div>
          </div>
          <div class="agency-details">
            <h3 class="agency-name">{{ agency.name }}</h3>
            <p v-if="agency.description" class="agency-description">{{ agency.description }}</p>
            <div class="agency-location" v-if="agency.city || agency.country">
              <i class="pi pi-map-marker location-icon"></i>
              <span>{{ agency.city }}{{ agency.city && agency.country ? ', ' : '' }}{{ agency.country }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import SidebarMenu from '../components/SidebarMenu.vue';
import AppHeader from '../components/layout/AppHeader.vue';
import apiService from '@/services/api';

const router = useRouter();
const isSidebarOpen = ref(false);
const agencies = ref<any[]>([]);
const isLoading = ref(true);
const error = ref<string | null>(null);
const searchQuery = ref('');

const toggleSidebar = () => {
  isSidebarOpen.value = !isSidebarOpen.value;
};

const closeSidebar = () => {
  isSidebarOpen.value = false;
};

// Function to fetch agencies from the API
const fetchAgencies = async (): Promise<void> => {
  try {
    isLoading.value = true;
    error.value = null;

    // Use the API service to fetch agencies
    const data = await apiService.agencies.getAll();

    // Transform the API data to match our frontend structure
    agencies.value = data.data.map((item: any) => ({
      id: item.id,
      name: item.name,
      description: item.short_description || item.description,
      logo: item.logo ? (item.logo.startsWith('/') ? item.logo : `/storage/${item.logo}`) : null,
      city: item.city,
      country: item.country,
      address: item.address,
      phone: item.phone,
      email: item.email,
      website: item.website
    }));
  } catch (err) {
    console.error('Error fetching agencies:', err);
    error.value = 'Error al cargar agencias. Por favor, inténtelo de nuevo más tarde.';

    // Fallback to sample data if API fails
    agencies.value = [
      {
        id: 1,
        name: 'Albatros Tours',
        description: 'Agencia especializada en turismo sostenible y experiencias en la naturaleza.',
        logo: '/assets/images/agencies/albatrostours.png',
        city: 'Badajoz',
        country: 'España',
        address: 'Calle Principal 123',
        phone: '+34 924 123 456',
        email: '<EMAIL>',
        website: 'https://albatrostours.com'
      },
      {
        id: 2,
        name: 'Cork Experience',
        description: 'La agencia oficial de Cork Experience, ofreciendo las mejores experiencias relacionadas con el corcho.',
        logo: '/assets/logos/CorkExpLogoBlack.png',
        city: 'Mérida',
        country: 'España',
        address: 'Avenida del Corcho 45',
        phone: '+34 924 987 654',
        email: '<EMAIL>',
        website: 'https://corkexperience.com'
      }
    ];
  } finally {
    isLoading.value = false;
  }
};

// Fetch agencies when component is mounted
onMounted(() => {
  fetchAgencies();
});

// Computed property for filtered agencies based on search query
const filteredAgencies = computed(() => {
  if (!searchQuery.value) return agencies.value;

  const query = searchQuery.value.toLowerCase();
  return agencies.value.filter(agency =>
    agency.name.toLowerCase().includes(query) ||
    (agency.description && agency.description.toLowerCase().includes(query)) ||
    (agency.city && agency.city.toLowerCase().includes(query)) ||
    (agency.country && agency.country.toLowerCase().includes(query))
  );
});

const viewAgencyDetails = (agency: any) => {
  router.push(`/agencias/${agency.id}`);
};
</script>

<style scoped>
.agencies-view {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  padding-bottom: 2rem;
}

.agencies-list {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
}

.agencies-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.agency-card {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.agency-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.agency-logo-container {
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f8f8;
  padding: 1rem;
}

.agency-logo {
  max-height: 100%;
  max-width: 100%;
  object-fit: contain;
}

.agency-logo-placeholder {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: #334960;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: bold;
}

.agency-details {
  padding: 1rem;
}

.agency-name {
  font-size: 1.2rem;
  font-weight: 600;
  color: #334960;
  margin-bottom: 0.5rem;
}

.agency-description {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.75rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.agency-location {
  display: flex;
  align-items: center;
  font-size: 0.8rem;
  color: #888;
}

.location-icon {
  margin-right: 0.25rem;
  color: #597694;
}

.no-results {
  text-align: center;
  padding: 2rem;
  color: #666;
  font-style: italic;
}

/* Loading state styles */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  flex: 1;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #597694;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Error state styles */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  flex: 1;
}

.error-message {
  color: #e74c3c;
  margin-bottom: 1rem;
}

.retry-button {
  background-color: #597694;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.retry-button:hover {
  background-color: #4a6380;
}

/* Responsive Styles */
@media (min-width: 640px) {
  .agencies-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .agencies-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
</style>
