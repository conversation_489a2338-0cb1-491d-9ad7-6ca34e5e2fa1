<template>
  <div class="experiences-view">
    <SidebarMenu :isOpen="isSidebarOpen" @close="closeSidebar" />
    <!-- Header -->
    <AppHeader
      :showLogo="true"
      logoSrc="/assets/logos/CorkExpLogoBlack.png"
      :showFavoriteButton="true"
      :showMenuButton="true"
      :showBackButton="true"
      @toggleMenu="toggleSidebar"
      @goBack="router.go(-1)"
    />

    <!-- Title -->
    <h1 class="experiences-title">{{ props.title }}</h1>

    <!-- Search Bar -->
    <div class="search-container">
      <div class="search-bar">
        <input
          type="text"
          v-model="searchQuery"
          placeholder="Buscar"
          class="search-input"
        >
        <button class="search-button">
          <i class="pi pi-search"></i>
        </button>
      </div>
    </div>

    <!-- View Tabs -->
    <div class="view-tabs" style="display: none;">
      <button
        class="tab-button"
        :class="{ 'active': activeView === 'list' }"
        @click="activeView = 'list'"
      >
        <i class="pi pi-list"></i>
      </button>
      <button
        class="tab-button"
        :class="{ 'active': activeView === 'map' }"
        @click="activeView = 'map'"
      >
        <i class="pi pi-map-marker"></i>
      </button>
    </div>

    <!-- Main Content -->
    <main class="main-content">
      <!-- List View -->
      <div v-if="activeView === 'list'" class="list-view">
        <!-- Loading state -->
        <div v-if="isLoading" class="loading-container">
          <div class="loading-spinner"></div>
          <p>Cargando experiencias...</p>
        </div>

        <!-- Error message -->
        <div v-else-if="error" class="error-container">
          <p class="error-message">{{ error }}</p>
          <button @click="fetchExperiences" class="retry-button">Reintentar</button>
        </div>

        <!-- No results -->
        <div v-else-if="filteredExperiences.length === 0" class="no-results">
          No se encontraron experiencias
        </div>

        <!-- Experiences list -->
        <div v-else class="experiences-list">
          <div
            v-for="experience in filteredExperiences"
            :key="experience.id"
            class="experience-card"
            @click="viewExperienceDetails(experience)"
          >
            <div class="experience-image-container">
              <img :src="experience.image" :alt="experience.title" class="experience-image" />
              <div v-if="experience.distance" class="experience-distance">{{ experience.distance }}</div>
            </div>
            <div class="experience-details">
              <h3 class="experience-title">{{ experience.title }}</h3>
              <p class="experience-description">{{ experience.shortDescription }}</p>
              <div class="experience-info">
                <div v-if="experience.date" class="experience-date">
                  <i class="pi pi-calendar"></i>
                  <span>{{ experience.date }}</span>
                </div>
                <div v-if="experience.location" class="experience-location">
                  <i class="pi pi-map-marker location-icon"></i>
                  <span>{{ experience.location }}</span>
                </div>
              </div>
              <div class="document-icon">
                <i class="pi pi-file"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Map View -->
      <div v-else-if="activeView === 'map'" class="map-view">
        <!-- Map placeholder - will be replaced with actual map component -->
        <div class="map-placeholder">
          <p>Mapa de experiencias</p>
          <p class="placeholder-note">Se implementará con un componente de mapa real</p>
        </div>
      </div>
    </main>

    <!-- Bottom Navigation removed as per design -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import AppHeader from '../components/layout/AppHeader.vue';
import SidebarMenu from '../components/SidebarMenu.vue';
import apiService from '@/services/api';
import type { ExperienceItem } from '@/services/api';

const props = defineProps({
  filterType: {
    type: String,
    default: ''
  },
  title: {
    type: String,
    default: 'Experiencias'
  }
});

const router = useRouter();
const activeView = ref('list');
const searchQuery = ref('');
const isSidebarOpen = ref(false);
const experiences = ref<any[]>([]);
const isLoading = ref(true);
const error = ref<string | null>(null);

const toggleSidebar = () => {
  isSidebarOpen.value = !isSidebarOpen.value;
};

const closeSidebar = () => {
  isSidebarOpen.value = false;
};

// Function to fetch experiences from the API
const fetchExperiences = async (): Promise<void> => {
  try {
    isLoading.value = true;
    error.value = null;

    // Use the API service to fetch experiences, filtered by type if specified
    const data = props.filterType
      ? await apiService.experiences.getByType(props.filterType)
      : await apiService.experiences.getAll();

    // Transform the API data to match our frontend structure
    experiences.value = data.data.map((item: ExperienceItem) => ({
      id: item.id,
      title: item.title,
      shortDescription: item.short_description || '',
      location: item.location ? item.location.name : '',
      date: item.start_date ? formatDate(item.start_date) : '',
      distance: item.distance || '',
      image: item.image ? (item.image.startsWith('/') ? item.image : `/assets/images/experiences/${item.image}`) : '/assets/images/experiences/default.jpeg',
      type: item.type,
      difficulty: item.difficulty,
      price: item.price,
      duration: item.duration
    }));
  } catch (err) {
    console.error('Error fetching experiences:', err);
    error.value = 'Error al cargar experiencias. Por favor, inténtelo de nuevo más tarde.';

    // Fallback to sample data if API fails
    experiences.value = [
      {
        id: 1,
        title: 'Alcornocales con ebike',
        shortDescription: 'El más grande alcornocal de la península ibérica y uno de los más importantes del mundo.',
        location: 'Castellar de la Frontera',
        date: '30/03/2025',
        distance: '12 km',
        image: '/assets/images/experiences/alcornocales.jpeg'
      },
      {
        id: 2,
        title: 'Cor de suro',
        shortDescription: 'Descubre el proceso de fabricación del corcho y su historia en esta fábrica tradicional.',
        location: 'Carrer de Germa Mari Esteller, 12528 Eslida, Castellón',
        date: '',
        distance: '5 km',
        image: '/assets/images/experiences/cordesuro.png'
      },
      {
        id: 3,
        title: 'Hotel Casa Convento',
        shortDescription: 'Alojamiento sostenible construido con materiales de corcho, ofreciendo una experiencia única.',
        location: 'Ctra Algeciras-ronda, s/n, 11350, Castellar de la Frontera',
        date: '',
        distance: '8 km',
        image: '/assets/images/experiences/hotelcasacormoran.jpeg'
      }
    ];
  } finally {
    isLoading.value = false;
  }
};

// Format date from API (YYYY-MM-DD) to DD/MM/YYYY
const formatDate = (dateString: string): string => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getFullYear()}`;
};

// Fetch experiences when component is mounted
onMounted(() => {
  fetchExperiences();
});

// Computed property for filtered experiences based on search query
const filteredExperiences = computed(() => {
  if (!searchQuery.value) return experiences.value;

  const query = searchQuery.value.toLowerCase();
  return experiences.value.filter(exp =>
    exp.title.toLowerCase().includes(query) ||
    exp.shortDescription.toLowerCase().includes(query) ||
    (exp.location && exp.location.toLowerCase().includes(query))
  );
});

const viewExperienceDetails = (experience: any) => {
  router.push(`/experiencias/${experience.id}`);
};
</script>

<style scoped>
.experiences-view {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.experiences-title {
  font-size: 1.5rem;
  color: #334960;
  margin: 1rem 1rem 0.5rem;
  font-weight: 500;
}

/* Header styles moved to AppHeader.vue component */

/* Search Bar Styles */
.search-container {
  padding: 0.5rem 1rem 1rem;
  background-color: transparent;
}

.search-bar {
  display: flex;
  align-items: center;
  background-color: #f0f2f5;
  border-radius: 8px;
  padding: 0;
  overflow: hidden;
  border: 1px solid #e0e0e0;
}

.search-input {
  flex: 1;
  border: none;
  background: none;
  font-size: 0.9rem;
  color: #333;
  outline: none;
  font-family: 'Poppins', sans-serif;
  padding: 0.75rem 1rem;
}

.search-button {
  background: none;
  border: none;
  padding: 0.75rem 1rem;
  color: #666;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-button i {
  font-size: 1.1rem;
}

/* View Tabs Styles */
.view-tabs {
  display: flex;
  justify-content: center;
  padding: 0.5rem;
  background-color: white;
  border-bottom: 1px solid #eee;
}

.tab-button {
  background: none;
  border: none;
  padding: 0.5rem 1.5rem;
  margin: 0 0.25rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
  color: #666;
}

.tab-button.active {
  background-color: #334960;
  color: white;
}

/* Main Content Styles */
.main-content {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
}

/* List View Styles */
.experiences-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.experience-card {
  display: flex;
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.3s, box-shadow 0.3s;
}

.experience-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.experience-image-container {
  position: relative;
  width: 100px;
  min-width: 100px;
  height: 100px;
}

.experience-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.experience-distance {
  position: absolute;
  bottom: 0.5rem;
  left: 0.5rem;
  background-color: rgba(51, 73, 96, 0.8);
  color: white;
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 500;
}

.experience-details {
  padding: 0.75rem;
  flex: 1;
  position: relative;
}

.experience-title {
  margin: 0 0 0.25rem;
  font-size: 0.9rem;
  font-weight: 600;
  color: #334960;
  font-family: 'Poppins', sans-serif;
}

.experience-description {
  margin: 0 0 0.5rem;
  font-size: 0.8rem;
  color: #666;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.experience-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-top: 0.5rem;
}

.experience-date,
.experience-location {
  display: flex;
  align-items: center;
  font-size: 0.75rem;
  color: #888;
}

.experience-date i,
.location-icon {
  margin-right: 0.25rem;
  font-size: 0.8rem;
}

.document-icon {
  position: absolute;
  top: 50%;
  right: 0.75rem;
  transform: translateY(-50%);
  width: 32px;
  height: 32px;
  background-color: #f0f2f5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.document-icon i {
  color: #597694;
  font-size: 1rem;
}

.no-results {
  text-align: center;
  padding: 2rem;
  color: #666;
  font-style: italic;
}

/* Loading state styles */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #597694;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Error state styles */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

.error-message {
  color: #e74c3c;
  margin-bottom: 1rem;
}

.retry-button {
  background-color: #597694;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.retry-button:hover {
  background-color: #4a6380;
}

/* Map View Styles */
.map-view {
  height: 100%;
  min-height: 300px;
}

.map-placeholder {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #e9ecef;
  border-radius: 8px;
  color: #666;
  text-align: center;
  padding: 2rem;
}

.placeholder-note {
  font-size: 0.8rem;
  font-style: italic;
  margin-top: 0.5rem;
  color: #888;
}

/* Bottom Navigation Styles removed */

/* Responsive Styles */
@media (min-width: 768px) {
  .experiences-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
  }

  .experience-card {
    flex-direction: column;
    height: 100%;
  }

  .experience-image-container {
    width: 100%;
    height: 150px;
  }
}


</style>
