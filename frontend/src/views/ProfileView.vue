<template>
  <div class="profile-view">
    <!-- Sidebar Menu (reused from HomeView) -->
    <SidebarMenu :is-open="isSidebarOpen" @close="closeSidebar" />

    <!-- Header -->
    <AppHeader
      title="Perfil"
      :showFavoriteButton="true"
      :showMenuButton="true"
      @toggleMenu="toggleSidebar"
    />

    <!-- Loading State -->
    <div v-if="isLoading" class="loading-container">
      <i class="pi pi-spin pi-spinner" style="font-size: 2rem"></i>
      <p>Cargando perfil...</p>
    </div>

    <!-- Profile Information -->
    <div v-else class="profile-info">
      <div class="profile-picture-container">
        <img
          :src="userProfilePicture"
          :alt="currentUser?.name + ' Profile Picture'"
          class="profile-picture"
        />
      </div>
      <div class="profile-details">
        <h2 class="profile-username">{{ currentUser?.name || 'Usuario' }}</h2>
        <p class="profile-email">{{ currentUser?.email || 'Sin correo electrónico' }}</p>
        <button class="profile-photo-button">
          <i class="pi pi-camera"></i>
          <span>Foto de perfil</span>
        </button>
      </div>
    </div>

    <!-- Profile Menu Options -->
    <div v-if="!isLoading" class="profile-menu">
      <div class="menu-item" @click="navigateTo('experiencias')">
        <span class="menu-text">Experiencias</span>
        <i class="pi pi-chevron-right"></i>
      </div>

      <div class="menu-item" @click="navigateTo('preferencias')">
        <span class="menu-text">Preferencias</span>
        <i class="pi pi-chevron-right"></i>
      </div>

      <div class="menu-item" @click="navigateTo('huella')">
        <span class="menu-text">Huella de carbono</span>
        <i class="pi pi-chevron-right"></i>
      </div>

      <div class="menu-item" @click="navigateTo('reservas')">
        <span class="menu-text">Mis Reservas</span>
        <i class="pi pi-chevron-right"></i>
      </div>

      <div class="menu-item" @click="navigateTo('recompensas')">
        <span class="menu-text">Recompensas</span>
        <i class="pi pi-chevron-right"></i>
      </div>

      <div class="menu-item" @click="navigateTo('favoritos')">
        <span class="menu-text">Favoritos</span>
        <i class="pi pi-chevron-right"></i>
      </div>

      <div class="menu-item" @click="navigateTo('configuracion')">
        <span class="menu-text">Configuración</span>
        <i class="pi pi-chevron-right"></i>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import SidebarMenu from '../components/SidebarMenu.vue';
import AppHeader from '../components/layout/AppHeader.vue';
import { currentUser, isAuthenticated } from '../services/auth';

// Router for navigation
const router = useRouter();

// State for sidebar
const isSidebarOpen = ref(false);
const isLoading = ref(true);

// Default profile picture
const defaultProfilePic = '/assets/images/profile/profilepicture.png';

// Computed property for user profile picture
const userProfilePicture = ref(defaultProfilePic);

// Check if user is authenticated
onMounted(() => {
  if (!isAuthenticated.value) {
    router.push('/login?redirect=/perfil');
    return;
  }

  // Set profile picture if available
  if (currentUser.value && 'profile_picture' in currentUser.value) {
    userProfilePicture.value = (currentUser.value as any).profile_picture;
  }

  isLoading.value = false;
});

const toggleSidebar = () => {
  isSidebarOpen.value = !isSidebarOpen.value;
};

const closeSidebar = () => {
  isSidebarOpen.value = false;
};

// Navigation function
const navigateTo = (destination: string) => {
  switch (destination) {
    case 'experiencias':
      router.push({ name: 'experiencias' });
      break;
    case 'preferencias':
      router.push({ name: 'preferencias' });
      break;
    case 'reservas':
      router.push('/reservas');
      break;
    case 'huella':
      // Placeholder - will be implemented later
      console.log('Navigate to huella de carbono');
      break;
    case 'recompensas':
      // Placeholder - will be implemented later
      console.log('Navigate to recompensas');
      break;
    case 'favoritos':
      // Placeholder - will be implemented later
      console.log('Navigate to favoritos');
      break;
    case 'configuracion':
      // Placeholder - will be implemented later
      console.log('Navigate to configuración');
      break;
    default:
      break;
  }
};
</script>

<style scoped>
.profile-view {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  gap: 1rem;
  margin-top: 2rem;
}

.profile-info {
  display: flex;
  align-items: center;
  padding: 1.5rem;
  background-color: white;
  margin-bottom: 1rem;
}

.profile-picture-container {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 1.5rem;
  border: 2px solid #e0e0e0;
}

.profile-picture {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-details {
  display: flex;
  flex-direction: column;
}

.profile-username {
  font-size: 1.25rem;
  font-weight: 600;
  color: #334960;
  margin: 0 0 0.25rem 0;
}

.profile-email {
  font-size: 0.9rem;
  color: #666;
  margin: 0 0 0.75rem 0;
}

.profile-photo-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background-color: #6366F1;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  cursor: pointer;
  width: fit-content;
}

.profile-photo-button i {
  font-size: 1rem;
}

.profile-menu {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 0 1rem;
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: white;
  padding: 1rem 1.25rem;
  border-radius: 8px;
  cursor: pointer;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.menu-text {
  font-size: 1rem;
  color: #334960;
  font-weight: 500;
}

.menu-item i {
  color: #9aa5b1;
  font-size: 1rem;
}
</style>
