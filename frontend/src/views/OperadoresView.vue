<template>
  <div class="operadores-view">
    <!-- Sidebar Menu -->
    <SidebarMenu :is-open="isSidebarOpen" @close="closeSidebar" />

    <!-- Header -->
    <AppHeader
      title="Operador"
      :showLogo="false"
      :showFavoriteButton="true"
      :showMenuButton="true"
      :leftAlignedTitle="true"
      @toggleMenu="toggleSidebar"
    />

    <!-- Main Content -->
    <main class="main-content">
      <!-- Welcome Notification for Video Call -->
      <NotificationCard :notification="videoCallNotification" class="notification-card" />

      <!-- Operator Logo and Map Section (side by side) -->
      <div class="operator-images">
        <div class="operator-logo-container">
          <img :src="operator.logo" :alt="operator.name" class="operator-logo" />
        </div>
        <div class="map-section">
          <img src="/assets/images/experiences/mapa.png" alt="Map Preview" class="map-preview" />
          <button class="map-button">Ver Mapa</button>
        </div>
      </div>

      <!-- Contact Information with Buttons -->
      <div class="info-card">
        <!-- Contact Buttons -->
        <div class="contact-buttons">
          <div class="button-container">
            <button class="contact-button">
              <i class="pi pi-envelope"></i>
            </button>
            <span class="button-label">Correo</span>
          </div>
          <div class="button-container">
            <button class="contact-button">
              <i class="pi pi-whatsapp"></i>
            </button>
            <span class="button-label">Mensaje</span>
          </div>
          <div class="button-container">
            <button class="contact-button">
              <i class="pi pi-phone"></i>
            </button>
            <span class="button-label">Llamadas</span>
          </div>
          <div class="button-container">
            <button class="contact-button">
              <i class="pi pi-globe"></i>
            </button>
            <span class="button-label">RRSS</span>
          </div>
        </div>

        <div class="info-section">
          <h3 class="info-title">Contacto</h3>
          <div class="info-content-row">
            <p class="info-content">{{ operator.contact }}</p>
            <a href="#" class="video-link">
              <i class="pi pi-play"></i>
              <span>Ver vídeo</span>
            </a>
          </div>
        </div>

        <div class="info-section">
          <h3 class="info-title">Dirección</h3>
          <p class="info-content">{{ operator.address }}</p>
        </div>

        <div class="info-section">
          <h3 class="info-title">Correo electrónico</h3>
          <p class="info-content">{{ operator.email }}</p>
        </div>

        <div class="info-section">
          <h3 class="info-title">Número telefónico</h3>
          <p class="info-content">{{ operator.phone }}</p>
        </div>

        <!-- Operator Description -->
        <div class="info-section">
          <h3 class="description-title">{{ operator.name }}</h3>
          <p class="description-content">{{ operator.description }}</p>
          <p class="website-link">{{ operator.website }}</p>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="action-section">
        <button class="action-button" @click="navigateToSugerencias">
          <span>Sugerencias</span>
          <i class="pi pi-chevron-right"></i>
        </button>
        <button class="action-button">
          <span>Consejos en destino</span>
          <i class="pi pi-chevron-right"></i>
        </button>
        <button class="action-button" @click="navigateToFAQs">
          <span>FAQS</span>
          <i class="pi pi-chevron-right"></i>
        </button>
      </div>

      <!-- No browser bar as it's just part of the Figma mockup -->
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import SidebarMenu from '../components/SidebarMenu.vue';
import NotificationCard from '../components/NotificationCard.vue';
import AppHeader from '../components/layout/AppHeader.vue';

const router = useRouter();

// Sidebar state
const isSidebarOpen = ref(false);

const toggleSidebar = () => {
  isSidebarOpen.value = !isSidebarOpen.value;
};

const closeSidebar = () => {
  isSidebarOpen.value = false;
};

// Navigation methods
const navigateToSugerencias = () => {
  router.push('/sugerencias');
};

const navigateToFAQs = () => {
  router.push('/faqs');
};

// Video call notification data
const videoCallNotification = {
  title: "Bienvenido",
  date: "10/07/2025",
  buttonText: "Entrar"
};

// Operator data
const operator = {
  name: "Albatros Tours",
  logo: "/assets/images/operators/albatrostours.png",
  contact: "Sara Pérez",
  address: "Calle del Pez 4, Cádiz",
  email: "<EMAIL>",
  phone: "+34 000 000 000 / +34 000 000 000",
  description: "Albatros Tours – Travel Experiences es la marca que mejor se adapta al ritmo del viajero actual. Por precio, calidad y flexibilidad, ofrece los mejores planes para disfrutar del Ocio haciendo una de las cosas que más nos gustan: ¡VIAJAR!",
  website: "https://tours-albatros.es/"
};
</script>

<style scoped>
.operadores-view {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.main-content {
  flex: 1;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Notification Card Styles */
.notification-card {
  margin-bottom: 0.5rem;
  width: 100%;
}

/* Operator Images Layout */
.operator-images {
  display: flex;
  margin-bottom: 0.5rem;
  border-radius: 8px;
  overflow: hidden;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  flex-direction: row;
  height: 150px;
}

/* Operator Logo Styles */
.operator-logo-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1rem;
  background-color: white;
  flex: 1;
  border-right: 1px solid #f0f0f0;
}

.operator-logo {
  max-width: 100%;
  height: auto;
  max-height: 120px;
  object-fit: contain;
}

/* Map Section Styles */
.map-section {
  position: relative;
  overflow: hidden;
  flex: 1;
}

.map-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.map-button {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background-color: #334960;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  font-size: 0.75rem;
  cursor: pointer;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Info Card Styles */
.info-card {
  background-color: white;
  border-radius: 8px;
  margin-bottom: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

/* Contact Buttons Styles */
.contact-buttons {
  display: flex;
  justify-content: space-between;
  background-color: white;
  padding: 1rem 1.5rem 0.5rem;
  position: relative;
}

/* No divider after contact buttons */

.button-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 3px;
}

.contact-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #F5F7FA;
  border: 1px solid #E4EBF2;
  border-radius: 6px;
  cursor: pointer;
  padding: 0;
  width: 60px;
  height: 45px;
  outline: none;
}

.contact-button i {
  font-size: 1.4rem;
  color: #597694;
}

.button-label {
  font-size: 0.65rem;
  color: #8BA8C7;
  font-weight: 400;
  margin-top: 2px;
}

/* Info Section Styles */
.info-section {
  background-color: white;
  padding: 1rem;
  position: relative;
}

.info-section:not(:last-child)::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 10%;
  right: 10%;
  height: 1px;
  background: linear-gradient(to right, transparent, #f0f0f0 20%, #f0f0f0 80%, transparent);
}

.info-title {
  color: #718EAD;
  font-size: 0.9rem;
  margin-bottom: 8px;
  font-weight: 300;
  letter-spacing: 4%;
  line-height: 160%;
}

.info-content {
  color: #334960;
  font-size: 0.9rem;
  margin: 0;
  line-height: 1.4;
}

.info-content-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.video-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #1AB7FF;
  font-size: 0.8rem;
  cursor: pointer;
  text-decoration: none;
  margin-right: 0.5rem;
  font-weight: 600;
}

.video-link i {
  font-size: 1rem;
  color: #1AB7FF;
}

/* Operator Description Styles */

.description-title {
  color: #334960;
  font-size: 1rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
  margin-top: 0.5rem;
}

.description-content {
  color: #334960;
  font-size: 0.85rem;
  line-height: 1.5;
  margin-bottom: 0.5rem;
  margin-top: 0;
}

.website-link {
  color: #334960;
  font-size: 0.85rem;
  margin: 0;
  word-break: break-all;
}

/* Action Buttons Styles */
.action-section {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.action-button {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: white;
  border: none;
  border-radius: 8px;
  padding: 1rem;
  font-size: 0.9rem;
  color: #334960;
  cursor: pointer;
  text-align: left;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.action-button i {
  color: #334960;
}

/* Browser bar styles removed as it's just part of the Figma mockup */
</style>
