<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import SidebarMenu from '../components/SidebarMenu.vue';
import NotificationCard from '../components/NotificationCard.vue';
import WeatherWidget from '../components/WeatherWidget.vue';
import AppHeader from '../components/layout/AppHeader.vue';
import LoginModal from '../components/auth/LoginModal.vue';
import appData from '../data/appData.json';
import apiService from '@/services/api';

const router = useRouter();
const events = ref<any[]>([]);
const isLoading = ref(true);
const error = ref<string | null>(null);

// Function to format date from API to the format used in the UI
const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  const month = date.toLocaleString('en-US', { month: 'short' });
  const day = date.getDate();
  return `${month}, ${day}`;
};

// Function to format time from API
const formatTime = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleTimeString('es-ES', { hour: '2-digit', minute: '2-digit' });
};

// Function to fetch news from the API
const fetchNews = async (): Promise<void> => {
  try {
    isLoading.value = true;
    error.value = null;

    // Use the API service to fetch news
    const data = await apiService.news.getAll();

    // Transform the API data to match our frontend structure
    events.value = data.data.map((item: any) => ({
      id: item.id,
      title: item.title,
      description: item.summary,
      date: formatDate(item.datetime),
      time: formatTime(item.datetime),
      location: item.location,
      rating: 4.5, // Default rating if not provided by API
      image: item.image.startsWith('/') ? item.image : `/assets/images/events/${item.image}`
    }));
  } catch (err) {
    console.error('Error fetching news:', err);
    error.value = 'Error al cargar las noticias. Por favor, inténtelo de nuevo más tarde.';

    // Fallback to sample data if API fails
    events.value = [
      {
        id: 1,
        title: '"Cork Experience: el corcho, un mundo por descubrir"',
        description: 'Es un proyecto que tiene como objetivo desarrollar nuevos productos turísticos sostenibles basados en el corcho.',
        date: 'Oct, 24',
        time: '14:30',
        location: 'Badajoz (España)',
        rating: 3.5,
        image: '/assets/images/events/CorkExpEvent.jpeg'
      },
      {
        id: 2,
        title: 'Taller de cajas nido con corcho natural',
        description: 'En este taller los participantes tendrán la oportunidad de desarrollar sus ideas y desarrollar herramientas tradicionales y actuales con corcho, de forma más sostenible y práctica.',
        date: 'Jul, 30',
        time: '15:30',
        location: 'Trujillo de Extremadura (España)',
        rating: 4.5,
        image: '/assets/images/events/CorkExpEvent2.jpeg'
      },
      {
        id: 3,
        title: 'Museo del Corcho de Cataluña: "El corcho en clave de mujer"',
        description: 'El Museo del Corcho es una institución que se dedica a la interpretación y difusión del patrimonio y los territorios vinculados al mundo del corcho.',
        date: 'May, 13',
        time: '13:00',
        location: 'Palafrugell (España)',
        rating: 5.0,
        image: '/assets/images/events/CorkExpEvent3.jpeg'
      }
    ];
  } finally {
    isLoading.value = false;
  }
};

// Fetch news when component is mounted
onMounted(() => {
  fetchNews();
});

import {
  isAuthenticated,
  isGuestMode,
  getGuestActions,
  executeGuestAction,
  handleFavoritesAccess
} from '@/services/auth';

const isSidebarOpen = ref(false);
const showLoginModal = ref(false);
const loginModalMessage = ref('');

const toggleSidebar = () => {
  isSidebarOpen.value = !isSidebarOpen.value;
};

const closeSidebar = () => {
  isSidebarOpen.value = false;
};

// Guest mode actions
const guestActions = getGuestActions();

// Handle guest action clicks
const handleGuestAction = (actionKey: string) => {
  if (actionKey === 'agenda') {
    router.push('/agenda');
  } else {
    executeGuestAction(actionKey);
  }
};

// Handle favorites button click with context awareness
const handleFavoritesClick = () => {
  const favoritesResult = handleFavoritesAccess();

  if (favoritesResult.requiresAuth) {
    // Show login modal for guest users
    loginModalMessage.value = favoritesResult.message;
    showLoginModal.value = true;
  } else {
    // Navigate to experiences with favorites filter for authenticated users
    router.push(favoritesResult.redirectTo);
  }
};

// Handle login button click
const handleLoginClick = () => {
  loginModalMessage.value = 'Inicia sesión para acceder a todas las funcionalidades de Cork Experience';
  showLoginModal.value = true;
};

// Handle login modal close
const handleLoginModalClose = () => {
  showLoginModal.value = false;
  loginModalMessage.value = '';
};

// Handle successful login
const handleLoginSuccess = () => {
  // Check for post-login redirect
  const redirectPath = localStorage.getItem('post_login_redirect');
  if (redirectPath) {
    localStorage.removeItem('post_login_redirect');
    router.push(redirectPath);
  }
};
</script>

<template>
  <div class="home-view">
    <!-- Sidebar Menu -->
    <SidebarMenu :is-open="isSidebarOpen" @close="closeSidebar" />

    <!-- Header -->
    <AppHeader
      :showProfileButton="isAuthenticated && !isGuestMode"
      :showLogo="true"
      logoSrc="/assets/logos/CorkExpLogoBlack.png"
      :showFavoriteButton="true"
      :showMenuButton="true"
      @toggleMenu="toggleSidebar"
      @favoritesClick="handleFavoritesClick"
    />

    <!-- Main Content -->
    <main class="main-content">
      <!-- Logged In User Content - Only shown for authenticated users who are not in guest mode -->
      <div v-if="isAuthenticated && !isGuestMode" class="logged-in-content">
        <!-- Welcome Notification -->
        <NotificationCard :notification="appData.welcomeMeet" class="notification-card" />

        <!-- Action Buttons -->
        <div class="action-buttons">
          <button class="action-button suggestions-button">
            Obtener Sugerencias
          </button>
          <button class="action-button tips-button">
            Consejos de Destino
          </button>
        </div>

        <!-- Weather Widget -->
        <WeatherWidget class="weather-widget" />
      </div>

      <!-- Guest Mode Content - Only shown for guest users -->
      <div v-if="isGuestMode" class="guest-content">
        <!-- Guest Welcome Message -->
        <div class="guest-welcome">
          <h2>¡Bienvenido a Cork Experience!</h2>
          <p>Explora nuestras experiencias únicas relacionadas con el corcho. Inicia sesión para acceder a todas las funcionalidades.</p>

          <!-- Login Button -->
          <button class="login-button" @click="handleLoginClick">
            <i class="pi pi-user"></i>
            Iniciar Sesión
          </button>
        </div>

        <!-- Guest Action Buttons -->
        <div class="guest-actions">
          <button
            v-for="action in guestActions"
            :key="action.key"
            class="guest-action-button"
            @click="handleGuestAction(action.key)"
          >
            <i :class="action.icon"></i>
            {{ action.label }}
          </button>
        </div>
      </div>

      <!-- Menu Cards -->
      <section class="menu-cards">
        <!-- Menu cards (same layout for both anonymous and logged in) -->
        <div class="card agenda-card" @click="router.push('/agenda')">
          <div class="card-icon">
            <img src="/assets/icons/Agenda.png" alt="Agenda" class="menu-icon" />
          </div>
          <div class="card-title">{{ isAuthenticated && !isGuestMode ? appData.loggedInMenuItems[0].title : 'Agenda' }}</div>
        </div>

        <div class="card map-card" @click="$router.push('/mapa')">
          <div class="card-icon">
            <img src="/assets/icons/Mapa.png" alt="Mapa" class="menu-icon" />
          </div>
          <div class="card-title">{{ isAuthenticated && !isGuestMode ? appData.loggedInMenuItems[1].title : 'Mapa' }}</div>
        </div>

        <div class="card experiences-card" @click="router.push('/experiencias')">
          <div class="card-icon">
            <img src="/assets/icons/Experiencias.png" alt="Experiencias" class="menu-icon" />
          </div>
          <div class="card-title" :style="{ textAlign: 'right' }">{{ isAuthenticated && !isGuestMode ? appData.loggedInMenuItems[2].title : 'Experiencias' }}</div>
        </div>
      </section>

      <!-- News and Events Section -->
      <section class="news-events-section">
        <div class="section-header">
          <h2>Noticias y eventos</h2>
          <a href="#" class="view-all">Ver agenda</a>
        </div>

        <!-- Loading state -->
        <div v-if="isLoading" class="loading-container">
          <div class="loading-spinner"></div>
          <p>Cargando noticias y eventos...</p>
        </div>

        <!-- Error message -->
        <div v-else-if="error" class="error-container">
          <p class="error-message">{{ error }}</p>
          <button @click="fetchNews" class="retry-button">Reintentar</button>
        </div>

        <!-- Events list -->
        <div v-else class="events-list">
          <div v-for="event in events" :key="event.id" class="event-card">
            <div class="event-date-container">
              <div class="event-image-background" :style="{ backgroundImage: `url(${event.image})` }"></div>
              <div class="event-date">
                <div class="month">{{ event.date.split(',')[0] }}</div>
                <div class="day">{{ event.date.split(',')[1].trim() }}</div>
              </div>
            </div>

            <div class="event-content">
              <h3 class="event-title">{{ event.title }}</h3>
              <p class="event-description">{{ event.description }}</p>

              <div class="event-details">
                <div class="event-time">
                  <i class="pi pi-clock"></i>
                  <span>{{ event.time }}</span>
                </div>

                <div class="event-location">
                  <i class="pi pi-map-marker"></i>
                  <span>{{ event.location }}</span>
                </div>

                <div class="event-rating">
                  <i class="pi pi-star"></i>
                  <span>({{ event.rating }})</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="view-more-container">
          <a href="#" class="view-more">Ver más</a>
        </div>

        <div class="contact-buttons">
          <button class="contact-button email-button" @click="handleGuestAction('email')">
            <i class="pi pi-envelope"></i>
            Enviar Email
          </button>
          <button class="contact-button call-button" @click="handleGuestAction('call')">
            <i class="pi pi-phone"></i>
            Llamar a la Agencia
          </button>
        </div>
      </section>
    </main>

    <!-- Login Modal -->
    <LoginModal
      :isVisible="showLoginModal"
      :message="loginModalMessage"
      @close="handleLoginModalClose"
      @loginSuccess="handleLoginSuccess"
    />
  </div>
</template>

<style scoped>
.home-view {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  padding-bottom: 2rem;
}

/* Header styles moved to AppHeader.vue component */

/* Main Content Styles */
.main-content {
  flex: 1;
  padding: 1rem 0;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Logged In Content Styles */
.logged-in-content {
  width: 90%;
  max-width: 600px;
  margin-bottom: 1.5rem;
  padding: 0 10px;
}

.notification-card {
  margin-bottom: 1rem;
}

.weather-widget {
  width: 100%;
  margin-top: 1rem;
}

.action-buttons {
  display: flex;
  justify-content: space-between;
  gap: 0.6rem;
  margin: 0 0 1rem;
  width: 100%;
}

.action-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.9rem;
  border: none;
  border-radius: 8px;
  background-color: #EDF2F7;
  color: #8da0cb;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  font-family: 'Poppins', sans-serif;
}

.action-button:hover {
  background-color: #e4ebf2;
}

.suggestions-button {
  background-color: #EDF2F7;
}

.tips-button {
  background-color: #EDF2F7;
}

/* Guest Mode Content Styles */
.guest-content {
  width: 90%;
  max-width: 600px;
  margin-bottom: 1.5rem;
  padding: 0 10px;
}

.guest-welcome {
  background-color: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
  border: 1px solid #e0e0e0;
}

.guest-welcome h2 {
  color: #333;
  font-size: 1.2rem;
  margin: 0 0 0.5rem 0;
  font-weight: 600;
}

.guest-welcome p {
  color: #666;
  font-size: 0.9rem;
  margin: 0 0 1rem 0;
  line-height: 1.4;
}

.login-button {
  background: linear-gradient(135deg, #597694 0%, #4a6380 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin: 0 auto;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(89, 118, 148, 0.3);
}

.login-button:hover {
  background: linear-gradient(135deg, #4a6380 0%, #3d5269 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(89, 118, 148, 0.4);
}

.guest-actions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.guest-action-button {
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  color: #597694;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.guest-action-button:hover {
  background-color: #f8f9fa;
  border-color: #597694;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.guest-action-button i {
  font-size: 1.1rem;
  color: #597694;
}

/* Menu Cards Styles */
.menu-cards {
  display: flex;
  justify-content: space-between;
  gap: 0.8rem;
  margin-bottom: 0.5rem;
  width: 90%;
  max-width: 600px;
  padding: 0 10px;
}

.card {
  background-color: white;
  border-radius: 8px;
  border: 1px solid #000;
  padding: 14px 12px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: space-between;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s, box-shadow 0.3s;
  position: relative;
  overflow: hidden;
  flex: 1;
  height: 90px !important;
  max-width: calc(33.33% - 0.6rem);
}

.agenda-card {
  background-image: url('/assets/backgrounds/cork-texture-1.png');
  background-size: cover;
}

.map-card {
  background-image: url('/assets/backgrounds/cork-texture-2.png');
  background-size: cover;
}

.experiences-card {
  background-image: url('/assets/backgrounds/cork-texture-3.png');
  background-size: cover;
}

.card-icon {
  z-index: 1;
  align-self: flex-start;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(0, 0, 0, 0.2);
  position: absolute;
  top: 14px;
  left: 12px;
}

.menu-icon {
  width: 32px;
  height: 32px;
  object-fit: contain;
}

.card-title {
  font-weight: 700 !important;
  color: #333;
  z-index: 1;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
  font-size: 0.85rem !important;
  align-self: flex-end;
  text-align: right;
  width: 100%;
  position: absolute;
  bottom: 14px;
  right: 12px;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* News and Events Section Styles */
.news-events-section {
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 0.6rem;
  width: 90%;
  max-width: 600px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.section-header h2 {
  font-size: 0.8rem;
  color: #8da0cb;
  margin: 0;
  font-weight: normal;
}

.section-header a {
  color: #8da0cb;
  text-decoration: none;
  font-size: 0.7rem;
}

.view-all {
  color: #8da0cb;
  text-decoration: none;
  font-size: 0.9rem;
}

.events-list {
  display: flex;
  flex-direction: column;
}

/* Loading state styles */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #597694;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Error state styles */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

.error-message {
  color: #e74c3c;
  margin-bottom: 1rem;
}

.retry-button {
  background-color: #597694;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.retry-button:hover {
  background-color: #4a6380;
}

.event-card {
  display: flex;
  padding: 1rem;
  border: 1px solid #ddd;
  margin-bottom: 0.75rem;
  background-color: white;
  border-radius: 6px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
  align-items: flex-start;
}

.event-date-container {
  margin-right: 1rem;
  margin-top: 0;
  width: 48px;
  height: 48px;
  flex-shrink: 0;
  position: relative;
}

.event-image-background {
  position: absolute;
  top: -6px;
  left: -6px;
  width: 60px;
  height: 80px;
  background-size: cover;
  background-position: center;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.event-date {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  text-align: center;
  border-radius: 6px;
  background-color: rgba(255, 255, 255, 0.8);
  position: relative;
  z-index: 2;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  margin-top: 10px;
  margin-left: -1.5px;
}

.month {
  font-size: 0.65rem;
  color: #666;
  text-transform: uppercase;
  width: 100%;
  padding: 3px 0 0;
  position: relative;
  z-index: 3;
  font-weight: 600;
  line-height: 1;
  margin-bottom: -2px;
}

.day {
  font-size: 1.4rem;
  font-weight: bold;
  color: #DC8960;
  padding: 0;
  flex-grow: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 3;
  width: 100%;
  line-height: 1;
  margin-top: -2px;
}

.event-content {
  flex: 1;
  padding-top: 0;
  margin-top: 0;
}

.event-title {
  font-size: 1rem;
  margin: 0 0 0.25rem 0;
  color: #333;
  font-weight: 600;
  line-height: 1.2;
}

.event-description {
  font-size: 0.6rem;
  color: #666;
  margin: 0 0 0.5rem 0;
  line-height: 1.2;
}

.event-details {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  font-size: 0.6rem;
  color: #666;
  margin-top: 0.25rem;
  width: 100%;
}

.event-time, .event-location, .event-rating {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  white-space: nowrap;
}

.event-time i, .event-location i, .event-rating i {
  font-size: 0.8rem;
  color: #555;
}

.view-more-container {
  text-align: right;
  margin-top: 0.5rem;
  margin-bottom: 1rem;
  width: 100%;
  max-width: 600px;
}

.view-more {
  color: #8da0cb;
  text-decoration: none;
  font-size: 0.8rem;
}

.contact-buttons {
  display: flex;
  justify-content: space-between;
  gap: 0.6rem;
  margin: 0 0.5rem 1rem;
  padding: 0 0.5rem;
}

.contact-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem;
  border: none;
  border-radius: 8px;
  background-color: #f5f5f5;
  color: #8da0cb;
  font-size: 0.6rem;
  cursor: pointer;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.email-button {
  background-color: #EDF2F7;
}

.call-button {
  background-color: #EDF2F7;
}

.contact-button i {
  font-size: 0.8rem;
  color: #8da0cb;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .menu-cards {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .card {
    flex: 1;
    min-width: 100px;
    height: 90px;
  }

  .card-icon {
    width: 40px;
    height: 40px;
  }

  .menu-icon {
    width: 24px;
    height: 24px;
  }

  .card-title {
    font-size: 0.7rem;
  }

  .event-date-container {
    min-width: 50px;
  }

  /* Keep action buttons side by side on all screen sizes */
  .action-buttons {
    gap: 0.5rem;
  }

  .action-button {
    font-size: 0.75rem;
    padding: 0.8rem 0.5rem;
  }
}

@media (max-width: 480px) {
  .logged-in-content {
    width: 95%;
  }

  .menu-cards {
    width: 95%;
  }

  .news-events-section {
    width: 95%;
  }

  .event-details {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .action-button {
    font-size: 0.7rem;
    padding: 0.7rem 0.4rem;
  }
}
</style>
