import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'
import { useAuthStore } from '../stores/auth'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'welcome',
      component: () => import('../components/welcome/WelcomeScreen.vue'),
    },
    {
      path: '/inicio',
      name: 'inicio',
      component: HomeView,
    },
    {
      path: '/home',
      redirect: '/inicio',
    },
    {
      path: '/login',
      redirect: '/inicio',
    },
    {
      path: '/agenda',
      name: 'agenda',
      component: () => import('../views/AgendaView.vue'),
    },
    {
      path: '/agenda/:id',
      name: 'event-detail',
      component: () => import('../views/EventDetailView.vue'),
    },
    {
      path: '/experiencias',
      name: 'experiencias',
      component: () => import('../views/ExperiencesView.vue'),
    },
    {
      path: '/experiencias/:id',
      name: 'experiencia-detalle',
      component: () => import('../views/ExperienceDetailView.vue'),
    },
    {
      path: '/experiences',
      redirect: '/experiencias',
    },
    {
      path: '/mapa',
      name: 'mapa',
      component: () => import('../views/MapView.vue'),
    },
    {
      path: '/operadores',
      redirect: '/agencias',
    },
    {
      path: '/sugerencias',
      name: 'sugerencias',
      component: () => import('../views/SugerenciasView.vue'),
    },
    {
      path: '/perfil',
      name: 'perfil',
      component: () => import('../views/ProfileView.vue'),
    },
    {
      path: '/profile',
      redirect: '/perfil',
    },
    {
      path: '/reservas',
      name: 'reservas',
      component: () => import('../views/UserReservationsView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/reservations',
      redirect: '/reservas',
    },
    {
      path: '/faqs',
      name: 'faqs',
      component: () => import('../views/FAQsView.vue'),
    },
    {
      path: '/preferencias',
      name: 'preferencias',
      component: () => import('../views/PreferencesView.vue'),
    },
    {
      path: '/storytours',
      name: 'storytours',
      component: () => import('../views/StoryToursView.vue'),
    },
    {
      path: '/storytours/:id',
      name: 'storytour-detalle',
      component: () => import('../views/StoryTourDetailView.vue'),
    },
    {
      path: '/alojamientos',
      name: 'alojamientos',
      component: () => import('../views/ExperiencesView.vue'),
      props: { filterType: 'hotel', title: 'Alojamiento' },
    },
    {
      path: '/restaurantes',
      name: 'restaurantes',
      component: () => import('../views/ExperiencesView.vue'),
      props: { filterType: 'restaurant', title: 'Restaurantes' },
    },
    {
      path: '/agencias',
      name: 'agencias',
      component: () => import('../views/AgenciesView.vue'),
    },

    // Guide Management Routes (Spanish URLs)
    {
      path: '/guia',
      name: 'guia-panel',
      component: () => import('../views/GuideDashboardView.vue'),
      meta: { requiresAuth: true, requiresGuideRole: true }
    },
    {
      path: '/guia/experiencias',
      name: 'guia-experiencias',
      component: () => import('../views/GuideExperiencesView.vue'),
      meta: { requiresAuth: true, requiresGuideRole: true }
    },
    {
      path: '/guia/grupos',
      name: 'guia-grupos',
      component: () => import('../views/GuideGroupsView.vue'),
      meta: { requiresAuth: true, requiresGuideRole: true }
    },
    {
      path: '/guia/perfil',
      name: 'guia-perfil',
      component: () => import('../views/GuideConfigView.vue'),
      meta: { requiresAuth: true, requiresGuideRole: true }
    },
    // English redirects for guide routes
    {
      path: '/guide',
      redirect: '/guia',
    },
    {
      path: '/guide/experiences',
      redirect: '/guia/experiencias',
    },
    {
      path: '/guide/groups',
      redirect: '/guia/grupos',
    },
    {
      path: '/guide/config',
      redirect: '/guia/perfil',
    },
    // Add grupos redirect for general groups access
    {
      path: '/grupos',
      name: 'grupos',
      component: () => import('../views/GuideGroupsView.vue'),
      meta: { requiresAuth: true, requiresGuideRole: true }
    },
    {
      path: '/groups',
      redirect: '/grupos',
    },
    {
      path: '/agencias/:id',
      name: 'agencia-detalle',
      component: () => import('../views/AgencyDetailView.vue'),
    },
    {
      path: '/rutas',
      name: 'rutas',
      component: () => import('../views/RoutesView.vue'),
    },
    {
      path: '/rutas/:id',
      name: 'ruta-detalle',
      component: () => import('../views/RouteDetailView.vue'),
    },
  ],
})

// Navigation guard for authentication and role-based access
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore();

  // Check authentication requirement
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next('/inicio');
    return;
  }

  // Check guide role requirement
  if (to.meta.requiresGuideRole) {
    const user = authStore.user;
    const isGuide = user?.role === 'guide' ||
                   user?.groups?.some((group: any) => group.is_guide);

    if (!isGuide) {
      // Redirect non-guides to home with error message
      next('/inicio?error=access_denied');
      return;
    }
  }

  next();
});

export default router
