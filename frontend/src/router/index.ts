import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'welcome',
      component: () => import('../components/welcome/WelcomeScreen.vue'),
    },
    {
      path: '/home',
      name: 'home',
      component: HomeView,
    },
    {
      path: '/login',
      redirect: '/home',
    },
    {
      path: '/agenda',
      name: 'agenda',
      component: () => import('../views/AgendaView.vue'),
    },
    {
      path: '/agenda/:id',
      name: 'event-detail',
      component: () => import('../views/EventDetailView.vue'),
    },
    {
      path: '/experiencias',
      name: 'experiencias',
      component: () => import('../views/ExperiencesView.vue'),
    },
    {
      path: '/experiencias/:id',
      name: 'experiencia-detalle',
      component: () => import('../views/ExperienceDetailView.vue'),
    },
    {
      path: '/experiences',
      redirect: '/experiencias',
    },
    {
      path: '/mapa',
      name: 'mapa',
      component: () => import('../views/MapView.vue'),
    },
    {
      path: '/operadores',
      redirect: '/agencias',
    },
    {
      path: '/sugerencias',
      name: 'sugerencias',
      component: () => import('../views/SugerenciasView.vue'),
    },
    {
      path: '/perfil',
      name: 'profile',
      component: () => import('../views/ProfileView.vue'),
    },
    {
      path: '/reservas',
      name: 'reservations',
      component: () => import('../views/UserReservationsView.vue'),
      meta: { requiresAuth: true }
    },

    {
      path: '/faqs',
      name: 'faqs',
      component: () => import('../views/FAQsView.vue'),
    },
    {
      path: '/perfil',
      name: 'perfil',
      component: () => import('../views/ProfileView.vue'),
    },
    {
      path: '/preferencias',
      name: 'preferencias',
      component: () => import('../views/PreferencesView.vue'),
    },
    {
      path: '/storytours',
      name: 'storytours',
      component: () => import('../views/StoryToursView.vue'),
    },
    {
      path: '/storytours/:id',
      name: 'storytour-detalle',
      component: () => import('../views/StoryTourDetailView.vue'),
    },
    {
      path: '/alojamientos',
      name: 'alojamientos',
      component: () => import('../views/ExperiencesView.vue'),
      props: { filterType: 'hotel', title: 'Alojamiento' },
    },
    {
      path: '/restaurantes',
      name: 'restaurantes',
      component: () => import('../views/ExperiencesView.vue'),
      props: { filterType: 'restaurant', title: 'Restaurantes' },
    },
    {
      path: '/agencias',
      name: 'agencias',
      component: () => import('../views/AgenciesView.vue'),
    },
    {
      path: '/agencias/:id',
      name: 'agencia-detalle',
      component: () => import('../views/AgencyDetailView.vue'),
    },
    {
      path: '/rutas',
      name: 'rutas',
      component: () => import('../views/RoutesView.vue'),
    },
    {
      path: '/rutas/:id',
      name: 'ruta-detalle',
      component: () => import('../views/RouteDetailView.vue'),
    },
  ],
})

export default router
