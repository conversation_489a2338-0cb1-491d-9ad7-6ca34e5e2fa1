import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'
import { useAuthStore } from '../stores/auth'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'welcome',
      component: () => import('../components/welcome/WelcomeScreen.vue'),
    },
    {
      path: '/home',
      name: 'home',
      component: HomeView,
    },
    {
      path: '/login',
      redirect: '/home',
    },
    {
      path: '/agenda',
      name: 'agenda',
      component: () => import('../views/AgendaView.vue'),
    },
    {
      path: '/agenda/:id',
      name: 'event-detail',
      component: () => import('../views/EventDetailView.vue'),
    },
    {
      path: '/experiencias',
      name: 'experiencias',
      component: () => import('../views/ExperiencesView.vue'),
    },
    {
      path: '/experiencias/:id',
      name: 'experiencia-detalle',
      component: () => import('../views/ExperienceDetailView.vue'),
    },
    {
      path: '/experiences',
      redirect: '/experiencias',
    },
    {
      path: '/mapa',
      name: 'mapa',
      component: () => import('../views/MapView.vue'),
    },
    {
      path: '/operadores',
      redirect: '/agencias',
    },
    {
      path: '/sugerencias',
      name: 'sugerencias',
      component: () => import('../views/SugerenciasView.vue'),
    },
    {
      path: '/perfil',
      name: 'profile',
      component: () => import('../views/ProfileView.vue'),
    },
    {
      path: '/reservas',
      name: 'reservations',
      component: () => import('../views/UserReservationsView.vue'),
      meta: { requiresAuth: true }
    },

    {
      path: '/faqs',
      name: 'faqs',
      component: () => import('../views/FAQsView.vue'),
    },
    {
      path: '/perfil',
      name: 'perfil',
      component: () => import('../views/ProfileView.vue'),
    },
    {
      path: '/preferencias',
      name: 'preferencias',
      component: () => import('../views/PreferencesView.vue'),
    },
    {
      path: '/storytours',
      name: 'storytours',
      component: () => import('../views/StoryToursView.vue'),
    },
    {
      path: '/storytours/:id',
      name: 'storytour-detalle',
      component: () => import('../views/StoryTourDetailView.vue'),
    },
    {
      path: '/alojamientos',
      name: 'alojamientos',
      component: () => import('../views/ExperiencesView.vue'),
      props: { filterType: 'hotel', title: 'Alojamiento' },
    },
    {
      path: '/restaurantes',
      name: 'restaurantes',
      component: () => import('../views/ExperiencesView.vue'),
      props: { filterType: 'restaurant', title: 'Restaurantes' },
    },
    {
      path: '/agencias',
      name: 'agencias',
      component: () => import('../views/AgenciesView.vue'),
    },

    // Guide Management Routes
    {
      path: '/guia',
      name: 'guide-dashboard',
      component: () => import('../views/GuideDashboardView.vue'),
      meta: { requiresAuth: true, requiresGuideRole: true }
    },
    {
      path: '/guia/experiencias',
      name: 'guide-experiences',
      component: () => import('../views/GuideExperiencesView.vue'),
      meta: { requiresAuth: true, requiresGuideRole: true }
    },
    {
      path: '/guia/grupos',
      name: 'guide-groups',
      component: () => import('../views/GuideGroupsView.vue'),
      meta: { requiresAuth: true, requiresGuideRole: true }
    },
    {
      path: '/guia/configuracion',
      name: 'guide-config',
      component: () => import('../views/GuideConfigView.vue'),
      meta: { requiresAuth: true, requiresGuideRole: true }
    },
    {
      path: '/agencias/:id',
      name: 'agencia-detalle',
      component: () => import('../views/AgencyDetailView.vue'),
    },
    {
      path: '/rutas',
      name: 'rutas',
      component: () => import('../views/RoutesView.vue'),
    },
    {
      path: '/rutas/:id',
      name: 'ruta-detalle',
      component: () => import('../views/RouteDetailView.vue'),
    },
  ],
})

// Navigation guard for authentication and role-based access
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore();

  // Check authentication requirement
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next('/home');
    return;
  }

  // Check guide role requirement
  if (to.meta.requiresGuideRole) {
    const user = authStore.user;
    const isGuide = user?.role === 'guide' ||
                   user?.groups?.some((group: any) => group.is_guide);

    if (!isGuide) {
      // Redirect non-guides to home with error message
      next('/home?error=access_denied');
      return;
    }
  }

  next();
});

export default router
