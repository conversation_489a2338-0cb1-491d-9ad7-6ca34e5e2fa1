<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Agency;
use App\Models\Experience;
use App\Models\Reservation;
use App\Notifications\SystemNotification;
use App\Notifications\SystemAlert;
use App\Notifications\ReservationCreated;
use App\Notifications\ReservationUpdated;
use App\Notifications\NewAgencyCreated;
use App\Notifications\NewAgencyUser;
use App\Services\NotificationService;
use App\Helpers\NotificationHelper;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;

class NotificationSystemTest extends TestCase
{
    use RefreshDatabase;

    protected $superadmin;
    protected $agency;
    protected $agencyAdmin;
    protected $regularUser;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test users with different roles
        $this->superadmin = User::factory()->create([
            'email' => '<EMAIL>',
            'role' => 'superadmin',
            'agency_id' => null,
        ]);

        $this->agency = Agency::factory()->create([
            'name' => 'Test Agency',
            'email' => '<EMAIL>',
        ]);

        $this->agencyAdmin = User::factory()->create([
            'email' => '<EMAIL>',
            'role' => 'agency_admin',
            'agency_id' => $this->agency->id,
        ]);

        $this->regularUser = User::factory()->create([
            'email' => '<EMAIL>',
            'role' => 'user',
            'agency_id' => null,
        ]);
    }

    /** @test */
    public function test_superadmin_notification_targeting()
    {
        Notification::fake();

        // Send notification to superadmins
        NotificationService::notifySuperadmins(new SystemAlert(
            'Test Alert',
            'This is a test alert for superadmins',
            'info'
        ));

        // Assert notification was sent only to superadmin
        Notification::assertSentTo($this->superadmin, SystemAlert::class);
        Notification::assertNotSentTo($this->agencyAdmin, SystemAlert::class);
        Notification::assertNotSentTo($this->regularUser, SystemAlert::class);
    }

    /** @test */
    public function test_agency_admin_notification_targeting()
    {
        Notification::fake();

        // Send notification to agency admins
        NotificationService::notifyAgencyAdmins(
            $this->agency->id,
            new SystemNotification('Test Agency Notification', 'Test message')
        );

        // Assert notification was sent only to agency admin
        Notification::assertSentTo($this->agencyAdmin, SystemNotification::class);
        Notification::assertNotSentTo($this->superadmin, SystemNotification::class);
        Notification::assertNotSentTo($this->regularUser, SystemNotification::class);
    }

    /** @test */
    public function test_reservation_created_notification()
    {
        Notification::fake();

        // Create an experience for the agency
        $experience = Experience::factory()->create([
            'agency_id' => $this->agency->id,
            'title' => 'Test Experience',
        ]);

        // Create a reservation
        $reservation = Reservation::factory()->create([
            'experience_id' => $experience->id,
            'user_id' => $this->regularUser->id,
            'num_people' => 2,
            'reservation_date' => now()->addDays(7),
        ]);

        // Load relationships
        $reservation->load(['user', 'experience.agency']);

        // Send notification as done in ReservationController
        NotificationService::notifyAgencyAdmins(
            $reservation->experience->agency_id,
            new ReservationCreated($reservation)
        );

        // Assert notification was sent to agency admin
        Notification::assertSentTo($this->agencyAdmin, ReservationCreated::class, function ($notification) use ($reservation) {
            return $notification->reservation->id === $reservation->id;
        });
    }

    /** @test */
    public function test_new_agency_created_notification()
    {
        Notification::fake();

        // Create a new agency
        $newAgency = Agency::factory()->create([
            'name' => 'New Test Agency',
            'email' => '<EMAIL>',
        ]);

        // Send notification as done in AgencyController
        NotificationService::notifySuperadmins(new NewAgencyCreated($newAgency));

        // Assert notification was sent to superadmin
        Notification::assertSentTo($this->superadmin, NewAgencyCreated::class, function ($notification) use ($newAgency) {
            return $notification->agency->id === $newAgency->id;
        });
    }

    /** @test */
    public function test_notification_content_is_in_spanish()
    {
        $reservation = Reservation::factory()->create([
            'experience_id' => Experience::factory()->create(['title' => 'Experiencia de Prueba'])->id,
            'user_id' => $this->regularUser->id,
            'num_people' => 3,
            'reservation_date' => now()->addDays(5),
        ]);

        $notification = new ReservationCreated($reservation);
        $mailMessage = $notification->toMail($this->agencyAdmin);

        // Check that content is in Spanish
        $this->assertStringContainsString('Nueva Reserva Creada', $mailMessage->subject);
        $this->assertStringContainsString('Se ha creado una nueva reserva', $mailMessage->introLines[0]);
        $this->assertStringContainsString('¡Gracias por usar Cork Experience!', $mailMessage->outroLines[0]);
    }

    /** @test */
    public function test_notification_helper_methods()
    {
        Notification::fake();

        // Test NotificationHelper methods
        NotificationHelper::notifySuperadmins(
            'Test Title',
            'Test Message',
            'Ver Detalles',
            '/dashboard',
            'info'
        );

        NotificationHelper::notifyAgency(
            $this->agency->id,
            'Agency Test',
            'Agency Message'
        );

        NotificationHelper::notifyUser(
            $this->regularUser,
            'User Test',
            'User Message'
        );

        // Assert notifications were sent correctly
        Notification::assertSentTo($this->superadmin, SystemNotification::class);
        Notification::assertSentTo($this->agencyAdmin, SystemNotification::class);
        Notification::assertSentTo($this->regularUser, SystemNotification::class);
    }

    /** @test */
    public function test_no_cross_role_notification_leakage()
    {
        Notification::fake();

        // Create another agency and admin
        $otherAgency = Agency::factory()->create(['name' => 'Other Agency']);
        $otherAgencyAdmin = User::factory()->create([
            'role' => 'agency_admin',
            'agency_id' => $otherAgency->id,
        ]);

        // Send notification to specific agency
        NotificationService::notifyAgencyAdmins(
            $this->agency->id,
            new SystemNotification('Agency Specific', 'This should only go to one agency')
        );

        // Assert notification was sent only to the correct agency admin
        Notification::assertSentTo($this->agencyAdmin, SystemNotification::class);
        Notification::assertNotSentTo($otherAgencyAdmin, SystemNotification::class);
        Notification::assertNotSentTo($this->superadmin, SystemNotification::class);
    }

    /** @test */
    public function test_notification_database_storage()
    {
        // Test that notifications are stored in database
        $this->superadmin->notify(new SystemNotification(
            'Database Test',
            'This notification should be stored in database',
            'Action',
            '/test-url',
            'info'
        ));

        // Check database
        $this->assertDatabaseHas('notifications', [
            'notifiable_id' => $this->superadmin->id,
            'notifiable_type' => User::class,
            'type' => SystemNotification::class,
        ]);

        // Check notification data
        $notification = $this->superadmin->notifications()->first();
        $this->assertEquals('Database Test', $notification->data['title']);
        $this->assertEquals('This notification should be stored in database', $notification->data['message']);
        $this->assertEquals('system', $notification->data['type']);
    }

    /** @test */
    public function test_notification_channels_configuration()
    {
        $notification = new SystemNotification('Test', 'Test message');
        $channels = $notification->via($this->superadmin);

        // Assert all expected channels are configured
        $this->assertContains('database', $channels);
        $this->assertContains('broadcast', $channels);
        $this->assertContains('mail', $channels);
    }

    /** @test */
    public function test_reservation_updated_notification()
    {
        Notification::fake();

        $experience = Experience::factory()->create(['agency_id' => $this->agency->id]);
        $reservation = Reservation::factory()->create([
            'experience_id' => $experience->id,
            'user_id' => $this->regularUser->id,
            'status' => 'confirmed',
        ]);

        $changes = ['status' => 'cancelled'];

        // Send notification as done in ReservationController
        NotificationService::notifyAgencyAdmins(
            $reservation->experience->agency_id,
            new ReservationUpdated($reservation, $changes)
        );

        // Assert notification was sent
        Notification::assertSentTo($this->agencyAdmin, ReservationUpdated::class);
    }

    /** @test */
    public function test_new_agency_user_notification()
    {
        Notification::fake();

        $newUser = User::factory()->create([
            'role' => 'agency_admin',
            'agency_id' => $this->agency->id,
        ]);

        // Load relationships
        $newUser->load('agency');

        // Send notification to other agency admins
        $this->agencyAdmin->notify(new NewAgencyUser($newUser));

        // Assert notification was sent
        Notification::assertSentTo($this->agencyAdmin, NewAgencyUser::class);
    }

    /** @test */
    public function test_notification_with_invalid_email_graceful_handling()
    {
        // Create user with invalid email format (this should be handled gracefully)
        $userWithInvalidEmail = User::factory()->create([
            'email' => 'invalid-email-format',
            'role' => 'superadmin',
        ]);

        // This should not throw an exception
        try {
            $userWithInvalidEmail->notify(new SystemNotification(
                'Test',
                'Test message for invalid email'
            ));
            $this->assertTrue(true); // Test passes if no exception is thrown
        } catch (\Exception $e) {
            $this->fail('Notification should handle invalid email gracefully: ' . $e->getMessage());
        }
    }

    /** @test */
    public function test_notification_action_urls_are_correct()
    {
        $reservation = Reservation::factory()->create([
            'experience_id' => Experience::factory()->create()->id,
            'user_id' => $this->regularUser->id,
        ]);

        $notification = new ReservationCreated($reservation);
        $mailMessage = $notification->toMail($this->agencyAdmin);

        // Check that action URL is correctly formatted
        $this->assertStringContainsString('/dashboard/reservations/' . $reservation->id, $mailMessage->actionUrl);
    }

    /** @test */
    public function test_system_alert_importance_levels()
    {
        $levels = ['info', 'warning', 'error', 'success'];

        foreach ($levels as $level) {
            $alert = new SystemAlert('Test Alert', 'Test message', $level);
            $mailMessage = $alert->toMail($this->superadmin);

            $this->assertStringContainsString('Alerta del Sistema', $mailMessage->subject);

            // Check that warning and error levels are properly handled
            if ($level === 'warning' || $level === 'error') {
                $this->assertNotNull($mailMessage);
            }
        }
    }

    /** @test */
    public function test_notification_mark_as_read_functionality()
    {
        // Create a notification
        $this->superadmin->notify(new SystemNotification(
            'Read Test',
            'This notification will be marked as read'
        ));

        $notification = $this->superadmin->notifications()->first();
        $this->assertNull($notification->read_at);

        // Mark as read
        $notification->markAsRead();

        $this->assertNotNull($notification->fresh()->read_at);
    }
}
