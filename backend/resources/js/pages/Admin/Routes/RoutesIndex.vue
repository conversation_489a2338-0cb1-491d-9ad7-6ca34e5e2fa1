<script lang="js" setup>
import AppLayout from '@/layouts/AppLayout.vue';
import { Head as InertiaHead } from '@inertiajs/vue3';

defineProps({
  message: {
    type: String,
    default: 'Esta sección está en mantenimiento. Por favor, inténtelo más tarde.'
  }
});
</script>

<template>
  <AppLayout>
    <InertiaHead title="Rutas" />
    <div class="container mx-auto py-8">
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h1 class="text-2xl font-semibold mb-4 text-gray-900 dark:text-white">Rutas</h1>
        <p class="mb-4 text-gray-600 dark:text-gray-400">Gestiona las rutas turísticas de la aplicación</p>

        <div class="mb-6">
          <a :href="route('admin.routes.create')" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
            Crear <PERSON>
          </a>
        </div>

        <div class="bg-yellow-100 dark:bg-yellow-900 p-6 rounded-lg border border-yellow-300 dark:border-yellow-700 mb-6">
          <h2 class="text-xl font-semibold text-yellow-800 dark:text-yellow-200 mb-2">Aviso</h2>
          <p class="text-yellow-700 dark:text-yellow-300">{{ message }}</p>
        </div>

        <div class="bg-gray-100 dark:bg-gray-700 p-4 rounded">
          <p class="text-center text-gray-600 dark:text-gray-400">No se encontraron rutas</p>
        </div>
      </div>
    </div>
  </AppLayout>
</template>
