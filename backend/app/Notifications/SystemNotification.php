<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Events\NotificationEvent;

class SystemNotification extends Notification
{
    use Queueable;

    protected $title;
    protected $message;
    protected $actionText;
    protected $actionUrl;
    protected $importance;

    /**
     * Create a new notification instance.
     */
    public function __construct(string $title, string $message, string $actionText = null, string $actionUrl = null, string $importance = 'info')
    {
        $this->title = $title;
        $this->message = $message;
        $this->actionText = $actionText;
        $this->actionUrl = $actionUrl;
        $this->importance = $importance; // 'info', 'warning', 'error', 'success'
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', 'broadcast', 'mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $mail = (new MailMessage)
            ->subject($this->title)
            ->line($this->message);

        if ($this->actionText && $this->actionUrl) {
            $mail->action($this->actionText, $this->actionUrl);
        }

        return $mail->line('¡Gracias por usar Cork Experience!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'title' => $this->title,
            'message' => $this->message,
            'action_text' => $this->actionText,
            'action_url' => $this->actionUrl,
            'importance' => $this->importance,
            'type' => 'system',
        ];
    }

    /**
     * Get the broadcastable representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toBroadcast($notifiable)
    {
        $data = $this->toArray($notifiable);

        // Dispatch a custom event for real-time updates
        event(new NotificationEvent($data, $notifiable->id));

        return $data;
    }
}
