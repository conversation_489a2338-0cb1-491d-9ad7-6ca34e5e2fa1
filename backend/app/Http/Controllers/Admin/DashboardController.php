<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Agency;
use App\Models\Experience;
use App\Models\Reservation;
use App\Models\User;
use App\Notifications\SystemNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class DashboardController extends Controller
{
    /**
     * Display the dashboard.
     */
    public function index()
    {
        $user = Auth::user();
        
        // Create a test notification for the current user
        $user->notify(new SystemNotification(
            'Bienvenido al sistema de notificaciones',
            'Este es un mensaje de prueba para verificar que las notificaciones funcionan correctamente.',
            'Ver Notificaciones',
            '/dashboard/notifications',
            'info'
        ));
        
        // Check if the user is a superadmin
        if ($user->role === 'superadmin') {
            return $this->superadminDashboard();
        }
        
        // Otherwise, show the agency dashboard
        return $this->agencyDashboard();
    }
    
    /**
     * Display the superadmin dashboard.
     */
    private function superadminDashboard()
    {
        // Get counts for the dashboard
        $agenciesCount = Agency::count();
        $experiencesCount = Experience::count();
        $usersCount = User::count();
        $reservationsCount = Reservation::count();
        
        // Get recent reservations
        $recentReservations = Reservation::with(['user', 'experience.agency'])
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();
        
        return Inertia::render('Admin/Dashboard', [
            'stats' => [
                'agencies' => $agenciesCount,
                'experiences' => $experiencesCount,
                'users' => $usersCount,
                'reservations' => $reservationsCount,
            ],
            'recentReservations' => $recentReservations,
        ]);
    }
    
    /**
     * Display the agency dashboard.
     */
    private function agencyDashboard()
    {
        $user = Auth::user();
        $agency = $user->agency;
        
        if (!$agency) {
            return Inertia::render('Admin/AgencyDashboard', [
                'stats' => [
                    'experiences' => 0,
                    'reservations' => 0,
                    'users' => 0,
                ],
                'recentReservations' => [],
            ]);
        }
        
        // Get counts for the dashboard
        $experiencesCount = Experience::where('agency_id', $agency->id)->count();
        $reservationsCount = Reservation::whereHas('experience', function ($query) use ($agency) {
            $query->where('agency_id', $agency->id);
        })->count();
        $usersCount = User::where('agency_id', $agency->id)->count();
        
        // Get recent reservations for this agency
        $recentReservations = Reservation::with(['user', 'experience'])
            ->whereHas('experience', function ($query) use ($agency) {
                $query->where('agency_id', $agency->id);
            })
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();
        
        return Inertia::render('Admin/AgencyDashboard', [
            'stats' => [
                'experiences' => $experiencesCount,
                'reservations' => $reservationsCount,
                'users' => $usersCount,
            ],
            'recentReservations' => $recentReservations,
        ]);
    }
}
