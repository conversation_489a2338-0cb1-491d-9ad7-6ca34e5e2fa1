<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Agency;
use App\Models\Experience;
use App\Models\Group;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class GuideController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth:sanctum');
        $this->middleware(function ($request, $next) {
            $user = Auth::user();
            
            // Check if user is a guide (either by role or group membership)
            if (!$user || (!$user->isGuide() && $user->role !== 'guide')) {
                return response()->json([
                    'message' => 'No tienes permisos para acceder a las funciones de guía'
                ], 403);
            }
            
            return $next($request);
        });
    }

    /**
     * Get dashboard statistics for the guide.
     */
    public function getDashboardStats(): JsonResponse
    {
        try {
            $user = Auth::user();
            $agencyId = $user->agency_id;

            // Get basic statistics
            $totalExperiences = Experience::where('agency_id', $agencyId)->count();
            $totalGroups = Group::where('agency_id', $agencyId)->count();
            $activeReservations = 0; // TODO: Implement when Reservation model is ready
            $averageRating = 4.5; // TODO: Calculate from actual ratings

            // Get recent activities (mock data for now)
            $recentActivities = [
                [
                    'id' => 1,
                    'type' => 'experience_created',
                    'description' => 'Nueva experiencia "Tour por el centro histórico" creada',
                    'created_at' => now()->subHours(2)->toISOString()
                ],
                [
                    'id' => 2,
                    'type' => 'group_created',
                    'description' => 'Nuevo grupo "Familia García" creado',
                    'created_at' => now()->subHours(5)->toISOString()
                ],
                [
                    'id' => 3,
                    'type' => 'reservation_created',
                    'description' => 'Nueva reserva para "Cata de vinos"',
                    'created_at' => now()->subDay()->toISOString()
                ]
            ];

            return response()->json([
                'data' => [
                    'total_experiences' => $totalExperiences,
                    'total_groups' => $totalGroups,
                    'active_reservations' => $activeReservations,
                    'average_rating' => $averageRating,
                    'recent_activities' => $recentActivities
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting guide dashboard stats', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'message' => 'Error al cargar las estadísticas del panel'
            ], 500);
        }
    }

    /**
     * Get agency configuration for white label customization.
     */
    public function getAgencyConfig(): JsonResponse
    {
        try {
            $user = Auth::user();
            $agency = $user->agency;

            if (!$agency) {
                return response()->json([
                    'message' => 'No se encontró la agencia asociada'
                ], 404);
            }

            // Return agency configuration with defaults
            $config = [
                'name' => $agency->name ?? 'Mi Agencia',
                'logo' => $agency->logo ?? '/assets/logos/CorkExpLogoBlack.png',
                'primary_color' => $agency->primary_color ?? '#2c5aa0',
                'secondary_color' => $agency->secondary_color ?? '#f39c12',
                'welcome_message' => $agency->welcome_message ?? 'Gestiona tus experiencias y grupos desde aquí',
                'contact_email' => $agency->email ?? '',
                'contact_phone' => $agency->phone ?? '',
                'website' => $agency->website ?? '',
                'address' => $agency->address ?? '',
                'allow_group_creation' => true,
                'allow_experience_editing' => true,
                'require_approval' => false,
                'enable_notifications' => true,
                'default_language' => 'es',
                'timezone' => 'Europe/Madrid',
                'currency' => 'EUR'
            ];

            return response()->json(['data' => $config]);
        } catch (\Exception $e) {
            Log::error('Error getting agency config', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'message' => 'Error al cargar la configuración de la agencia'
            ], 500);
        }
    }

    /**
     * Update agency configuration.
     */
    public function updateAgencyConfig(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $agency = $user->agency;

            if (!$agency) {
                return response()->json([
                    'message' => 'No se encontró la agencia asociada'
                ], 404);
            }

            // Validate the request
            $validated = $request->validate([
                'name' => 'sometimes|string|max:255',
                'welcome_message' => 'sometimes|string|max:500',
                'primary_color' => 'sometimes|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'secondary_color' => 'sometimes|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'logo' => 'sometimes|url',
                'contact_email' => 'sometimes|email',
                'contact_phone' => 'sometimes|string|max:20',
                'website' => 'sometimes|url',
                'address' => 'sometimes|string|max:500',
            ]);

            // Update agency with validated data
            $agency->update($validated);

            Log::info('Agency configuration updated', [
                'user_id' => Auth::id(),
                'agency_id' => $agency->id,
                'updated_fields' => array_keys($validated)
            ]);

            return response()->json([
                'message' => 'Configuración actualizada correctamente',
                'data' => $agency->fresh()
            ]);
        } catch (\Exception $e) {
            Log::error('Error updating agency config', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'message' => 'Error al actualizar la configuración'
            ], 500);
        }
    }

    /**
     * Get experiences managed by the guide.
     */
    public function getMyExperiences(): JsonResponse
    {
        try {
            $user = Auth::user();
            $agencyId = $user->agency_id;

            $experiences = Experience::where('agency_id', $agencyId)
                ->with(['location'])
                ->get();

            return response()->json(['data' => $experiences]);
        } catch (\Exception $e) {
            Log::error('Error getting guide experiences', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'message' => 'Error al cargar las experiencias'
            ], 500);
        }
    }

    /**
     * Get groups managed by the guide.
     */
    public function getMyGroups(): JsonResponse
    {
        try {
            $user = Auth::user();
            $agencyId = $user->agency_id;

            $groups = Group::where('agency_id', $agencyId)
                ->with(['members'])
                ->get();

            return response()->json(['data' => $groups]);
        } catch (\Exception $e) {
            Log::error('Error getting guide groups', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'message' => 'Error al cargar los grupos'
            ], 500);
        }
    }

    /**
     * Get available restaurants for assignment.
     */
    public function getAvailableRestaurants(): JsonResponse
    {
        try {
            $user = Auth::user();
            $agencyId = $user->agency_id;

            // For now, return mock restaurant data
            // TODO: Implement actual restaurant model and relationships
            $restaurants = [
                [
                    'id' => 1,
                    'name' => 'Restaurante El Rincón',
                    'description' => 'Cocina tradicional española con productos locales',
                    'cuisine_type' => 'Española',
                    'rating' => 4.5,
                    'price_range' => 'medium',
                    'address' => 'Calle Mayor 15, Madrid',
                    'website' => 'https://elrincon.com',
                    'menu_url' => 'https://elrincon.com/menu',
                    'opening_hours' => '12:00 - 23:00',
                    'image' => '/assets/images/restaurants/el-rincon.jpg'
                ],
                [
                    'id' => 2,
                    'name' => 'Taberna Los Arcos',
                    'description' => 'Tapas y vinos en ambiente acogedor',
                    'cuisine_type' => 'Tapas',
                    'rating' => 4.2,
                    'price_range' => 'low',
                    'address' => 'Plaza de Armas 8, Madrid',
                    'website' => 'https://losarcos.com',
                    'opening_hours' => '18:00 - 02:00',
                    'image' => '/assets/images/restaurants/los-arcos.jpg'
                ]
            ];

            return response()->json(['data' => $restaurants]);
        } catch (\Exception $e) {
            Log::error('Error getting available restaurants', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'message' => 'Error al cargar los restaurantes'
            ], 500);
        }
    }
}
