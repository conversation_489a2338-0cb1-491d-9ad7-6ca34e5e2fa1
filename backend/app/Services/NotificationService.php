<?php

namespace App\Services;

use App\Models\User;
use App\Models\Agency;
use App\Notifications\SystemAlert;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;
use Exception;

class NotificationService
{
    /**
     * Send a notification to all superadmins.
     *
     * @param Notification $notification
     * @return array Array with success count and failed count
     */
    public static function notifySuperadmins(Notification $notification): array
    {
        try {
            $superadmins = User::where('role', 'superadmin')->get();

            if ($superadmins->isEmpty()) {
                Log::warning('No superadmins found to send notification', [
                    'notification_type' => get_class($notification)
                ]);
                return ['success' => 0, 'failed' => 0, 'message' => 'No superadmins found'];
            }

            $successCount = 0;
            $failedCount = 0;

            foreach ($superadmins as $admin) {
                try {
                    if (!$admin->email || !filter_var($admin->email, FILTER_VALIDATE_EMAIL)) {
                        Log::warning('Superadmin has invalid email', [
                            'user_id' => $admin->id,
                            'email' => $admin->email
                        ]);
                        $failedCount++;
                        continue;
                    }

                    $admin->notify($notification);
                    $successCount++;
                } catch (Exception $e) {
                    Log::error('Failed to send notification to superadmin', [
                        'user_id' => $admin->id,
                        'email' => $admin->email,
                        'error' => $e->getMessage(),
                        'notification_type' => get_class($notification)
                    ]);
                    $failedCount++;
                }
            }

            Log::info('Superadmin notifications sent', [
                'success_count' => $successCount,
                'failed_count' => $failedCount,
                'notification_type' => get_class($notification)
            ]);

            return ['success' => $successCount, 'failed' => $failedCount];

        } catch (Exception $e) {
            Log::error('Critical error in notifySuperadmins', [
                'error' => $e->getMessage(),
                'notification_type' => get_class($notification)
            ]);
            return ['success' => 0, 'failed' => 1, 'message' => 'Critical error occurred'];
        }
    }

    /**
     * Send a notification to all users of a specific agency.
     *
     * @param int $agencyId
     * @param Notification $notification
     * @return array Array with success count and failed count
     */
    public static function notifyAgencyUsers(int $agencyId, Notification $notification): array
    {
        try {
            // Validate agency exists
            $agency = Agency::find($agencyId);
            if (!$agency) {
                Log::warning('Agency not found for notification', [
                    'agency_id' => $agencyId,
                    'notification_type' => get_class($notification)
                ]);
                return ['success' => 0, 'failed' => 0, 'message' => 'Agency not found'];
            }

            $agencyUsers = User::where('agency_id', $agencyId)->get();

            if ($agencyUsers->isEmpty()) {
                Log::info('No users found for agency', [
                    'agency_id' => $agencyId,
                    'agency_name' => $agency->name
                ]);
                return ['success' => 0, 'failed' => 0, 'message' => 'No users found for agency'];
            }

            $successCount = 0;
            $failedCount = 0;

            foreach ($agencyUsers as $user) {
                try {
                    if (!$user->email || !filter_var($user->email, FILTER_VALIDATE_EMAIL)) {
                        Log::warning('Agency user has invalid email', [
                            'user_id' => $user->id,
                            'email' => $user->email,
                            'agency_id' => $agencyId
                        ]);
                        $failedCount++;
                        continue;
                    }

                    $user->notify($notification);
                    $successCount++;
                } catch (Exception $e) {
                    Log::error('Failed to send notification to agency user', [
                        'user_id' => $user->id,
                        'email' => $user->email,
                        'agency_id' => $agencyId,
                        'error' => $e->getMessage(),
                        'notification_type' => get_class($notification)
                    ]);
                    $failedCount++;
                }
            }

            Log::info('Agency user notifications sent', [
                'agency_id' => $agencyId,
                'success_count' => $successCount,
                'failed_count' => $failedCount,
                'notification_type' => get_class($notification)
            ]);

            return ['success' => $successCount, 'failed' => $failedCount];

        } catch (Exception $e) {
            Log::error('Critical error in notifyAgencyUsers', [
                'agency_id' => $agencyId,
                'error' => $e->getMessage(),
                'notification_type' => get_class($notification)
            ]);
            return ['success' => 0, 'failed' => 1, 'message' => 'Critical error occurred'];
        }
    }
