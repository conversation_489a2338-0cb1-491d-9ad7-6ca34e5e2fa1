<?php

namespace App\Services;

use App\Models\User;
use App\Models\Agency;
use App\Notifications\SystemAlert;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Collection;
use Exception;
use App\Events\NotificationEvent;

class NotificationService
{
    /**
     * Send a notification to all superadmins.
     *
     * @param Notification $notification
     * @return array Array with success count and failed count
     */
    public static function notifySuperadmins(Notification $notification): array
    {
        try {
            $superadmins = User::where('role', 'superadmin')->get();

            if ($superadmins->isEmpty()) {
                Log::warning('No superadmins found to send notification', [
                    'notification_type' => get_class($notification)
                ]);
                return ['success' => 0, 'failed' => 0, 'message' => 'No superadmins found'];
            }

            return self::sendNotificationToUsers($superadmins, $notification, 'superadmin');

        } catch (Exception $e) {
            Log::error('Critical error in notifySuperadmins', [
                'error' => $e->getMessage(),
                'notification_type' => get_class($notification)
            ]);
            return ['success' => 0, 'failed' => 1, 'message' => 'Critical error occurred'];
        }
    }

    /**
     * Send a notification to all users of a specific agency.
     *
     * @param int $agencyId
     * @param Notification $notification
     * @return array Array with success count and failed count
     */
    public static function notifyAgencyUsers(int $agencyId, Notification $notification): array
    {
        try {
            // Validate agency exists
            $agency = Agency::find($agencyId);
            if (!$agency) {
                Log::warning('Agency not found for notification', [
                    'agency_id' => $agencyId,
                    'notification_type' => get_class($notification)
                ]);
                return ['success' => 0, 'failed' => 0, 'message' => 'Agency not found'];
            }

            $agencyUsers = User::where('agency_id', $agencyId)->get();

            if ($agencyUsers->isEmpty()) {
                Log::info('No users found for agency', [
                    'agency_id' => $agencyId,
                    'agency_name' => $agency->name
                ]);
                return ['success' => 0, 'failed' => 0, 'message' => 'No users found for agency'];
            }

            return self::sendNotificationToUsers($agencyUsers, $notification, 'agency', $agencyId);

        } catch (Exception $e) {
            Log::error('Critical error in notifyAgencyUsers', [
                'agency_id' => $agencyId,
                'error' => $e->getMessage(),
                'notification_type' => get_class($notification)
            ]);
            return ['success' => 0, 'failed' => 1, 'message' => 'Critical error occurred'];
        }
    }

    /**
     * Send a notification to agency admins only.
     *
     * @param int $agencyId
     * @param Notification $notification
     * @return array Array with success count and failed count
     */
    public static function notifyAgencyAdmins(int $agencyId, Notification $notification): array
    {
        try {
            // Validate agency exists
            $agency = Agency::find($agencyId);
            if (!$agency) {
                Log::warning('Agency not found for admin notification', [
                    'agency_id' => $agencyId,
                    'notification_type' => get_class($notification)
                ]);
                return ['success' => 0, 'failed' => 0, 'message' => 'Agency not found'];
            }

            $agencyAdmins = User::where('agency_id', $agencyId)
                               ->whereIn('role', ['admin', 'agency_admin'])
                               ->get();

            if ($agencyAdmins->isEmpty()) {
                Log::info('No agency admins found', [
                    'agency_id' => $agencyId,
                    'agency_name' => $agency->name
                ]);
                return ['success' => 0, 'failed' => 0, 'message' => 'No agency admins found'];
            }

            return self::sendNotificationToUsers($agencyAdmins, $notification, 'agency_admin', $agencyId);

        } catch (Exception $e) {
            Log::error('Critical error in notifyAgencyAdmins', [
                'agency_id' => $agencyId,
                'error' => $e->getMessage(),
                'notification_type' => get_class($notification)
            ]);
            return ['success' => 0, 'failed' => 1, 'message' => 'Critical error occurred'];
        }
    }

    /**
     * Send a notification to a specific user.
     *
     * @param int $userId
     * @param Notification $notification
     * @return array Array with success count and failed count
     */
    public static function notifyUser(int $userId, Notification $notification): array
    {
        try {
            $user = User::find($userId);
            
            if (!$user) {
                Log::warning('User not found for notification', [
                    'user_id' => $userId,
                    'notification_type' => get_class($notification)
                ]);
                return ['success' => 0, 'failed' => 0, 'message' => 'User not found'];
            }

            return self::sendNotificationToUsers(collect([$user]), $notification, 'individual');

        } catch (Exception $e) {
            Log::error('Critical error in notifyUser', [
                'user_id' => $userId,
                'error' => $e->getMessage(),
                'notification_type' => get_class($notification)
            ]);
            return ['success' => 0, 'failed' => 1, 'message' => 'Critical error occurred'];
        }
    }

    /**
     * Send a notification to multiple agencies.
     *
     * @param array $agencyIds
     * @param Notification $notification
     * @return array Array with success count and failed count
     */
    public static function notifyMultipleAgencies(array $agencyIds, Notification $notification): array
    {
        $totalSuccess = 0;
        $totalFailed = 0;
        $results = [];

        foreach ($agencyIds as $agencyId) {
            $result = self::notifyAgencyUsers($agencyId, $notification);
            $totalSuccess += $result['success'];
            $totalFailed += $result['failed'];
            $results[] = ['agency_id' => $agencyId, 'result' => $result];
        }

        Log::info('Multiple agency notifications completed', [
            'agency_ids' => $agencyIds,
            'total_success' => $totalSuccess,
            'total_failed' => $totalFailed,
            'notification_type' => get_class($notification),
            'detailed_results' => $results
        ]);

        return ['success' => $totalSuccess, 'failed' => $totalFailed, 'details' => $results];
    }

    /**
     * Send notifications based on user roles.
     *
     * @param array $roles
     * @param Notification $notification
     * @param int|null $agencyId Optional agency filter
     * @return array Array with success count and failed count
     */
    public static function notifyByRole(array $roles, Notification $notification, ?int $agencyId = null): array
    {
        try {
            $query = User::whereIn('role', $roles);
            
            if ($agencyId) {
                $query->where('agency_id', $agencyId);
            }
            
            $users = $query->get();

            if ($users->isEmpty()) {
                Log::info('No users found for roles', [
                    'roles' => $roles,
                    'agency_id' => $agencyId,
                    'notification_type' => get_class($notification)
                ]);
                return ['success' => 0, 'failed' => 0, 'message' => 'No users found for specified roles'];
            }

            return self::sendNotificationToUsers($users, $notification, 'role_based', $agencyId);

        } catch (Exception $e) {
            Log::error('Critical error in notifyByRole', [
                'roles' => $roles,
                'agency_id' => $agencyId,
                'error' => $e->getMessage(),
                'notification_type' => get_class($notification)
            ]);
            return ['success' => 0, 'failed' => 1, 'message' => 'Critical error occurred'];
        }
    }

    /**
     * Send system-wide announcement to all users (excluding specific roles if needed).
     *
     * @param Notification $notification
     * @param array $excludeRoles Roles to exclude from notification
     * @return array Array with success count and failed count
     */
    public static function sendSystemAnnouncement(Notification $notification, array $excludeRoles = []): array
    {
        try {
            $query = User::query();
            
            if (!empty($excludeRoles)) {
                $query->whereNotIn('role', $excludeRoles);
            }
            
            $users = $query->get();

            if ($users->isEmpty()) {
                Log::info('No users found for system announcement', [
                    'exclude_roles' => $excludeRoles,
                    'notification_type' => get_class($notification)
                ]);
                return ['success' => 0, 'failed' => 0, 'message' => 'No users found'];
            }

            return self::sendNotificationToUsers($users, $notification, 'system_announcement');

        } catch (Exception $e) {
            Log::error('Critical error in sendSystemAnnouncement', [
                'exclude_roles' => $excludeRoles,
                'error' => $e->getMessage(),
                'notification_type' => get_class($notification)
            ]);
            return ['success' => 0, 'failed' => 1, 'message' => 'Critical error occurred'];
        }
    }

    /**
     * Private helper method to send notifications to a collection of users.
     *
     * @param Collection $users
     * @param Notification $notification
     * @param string $context
     * @param int|null $agencyId
     * @return array
     */
    private static function sendNotificationToUsers(Collection $users, Notification $notification, string $context, ?int $agencyId = null): array
    {
        $successCount = 0;
        $failedCount = 0;

        foreach ($users as $user) {
            try {
                if (!$user->email || !filter_var($user->email, FILTER_VALIDATE_EMAIL)) {
                    Log::warning('User has invalid email', [
                        'user_id' => $user->id,
                        'email' => $user->email,
                        'context' => $context,
                        'agency_id' => $agencyId
                    ]);
                    $failedCount++;
                    continue;
                }

                // Check if user is active (assuming you have an 'active' or 'status' field)
                if (isset($user->status) && $user->status !== 'active') {
                    Log::info('Skipping inactive user', [
                        'user_id' => $user->id,
                        'status' => $user->status,
                        'context' => $context
                    ]);
                    continue;
                }

                $user->notify($notification);
                $successCount++;
                
            } catch (Exception $e) {
                Log::error('Failed to send notification to user', [
                    'user_id' => $user->id,
                    'email' => $user->email,
                    'context' => $context,
                    'agency_id' => $agencyId,
                    'error' => $e->getMessage(),
                    'notification_type' => get_class($notification)
                ]);
                $failedCount++;
            }
        }

        Log::info('Notifications sent', [
            'context' => $context,
            'agency_id' => $agencyId,
            'success_count' => $successCount,
            'failed_count' => $failedCount,
            'notification_type' => get_class($notification)
        ]);

        return ['success' => $successCount, 'failed' => $failedCount];
    }

    /**
     * Get notification statistics for monitoring purposes.
     *
     * @return array
     */
    public static function getNotificationStats(): array
    {
        try {
            return [
                'total_users' => User::count(),
                'active_users' => User::where('status', 'active')->count(),
                'superadmins' => User::where('role', 'superadmin')->count(),
                'agency_admins' => User::whereIn('role', ['admin', 'agency_admin'])->count(),
                'total_agencies' => Agency::count(),
                'agencies_with_users' => Agency::has('users')->count(),
            ];
        } catch (Exception $e) {
            Log::error('Error getting notification stats', ['error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * Create and send a system notification to superadmins.
     */
    public static function sendSystemNotificationToSuperadmins(
        string $title,
        string $message,
        ?string $actionText = null,
        ?string $actionUrl = null,
        string $importance = 'info',
        bool $broadcast = true
    ): array {
        $notification = new SystemNotification($title, $message, $actionText, $actionUrl, $importance);
        return self::notifySuperadmins($notification, $broadcast);
    }

    /**
     * Create and send a system notification to agency users.
     */
    public static function sendSystemNotificationToAgency(
        int $agencyId,
        string $title,
        string $message,
        ?string $actionText = null,
        ?string $actionUrl = null,
        string $importance = 'info',
        bool $broadcast = true
    ): array {
        $notification = new SystemNotification($title, $message, $actionText, $actionUrl, $importance);
        return self::notifyAgencyUsers($agencyId, $notification, $broadcast);
    }

    /**
     * Create and send a system notification to agency admins.
     */
    public static function sendSystemNotificationToAgencyAdmins(
        int $agencyId,
        string $title,
        string $message,
        ?string $actionText = null,
        ?string $actionUrl = null,
        string $importance = 'info',
        bool $broadcast = true
    ): array {
        $notification = new SystemNotification($title, $message, $actionText, $actionUrl, $importance);
        return self::notifyAgencyAdmins($agencyId, $notification, $broadcast);
    }

    /**
     * Broadcast a notification for real-time updates.
     */
    private static function broadcastNotification(User $user, SystemNotification $notification): void
    {
        try {
            $notificationData = [
                'id' => uniqid(),
                'title' => $notification->title,
                'message' => $notification->message,
                'importance' => $notification->importance,
                'action_text' => $notification->actionText,
                'action_url' => $notification->actionUrl,
                'created_at' => now()->toISOString(),
                'read' => false
            ];

            event(new NotificationEvent($notificationData, $user->id));
            
        } catch (Exception $e) {
            Log::error('Failed to broadcast notification', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
        }
    }
}