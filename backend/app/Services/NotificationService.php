<?php

namespace App\Services;

use App\Models\User;
use App\Notifications\SystemAlert;
use Illuminate\Notifications\Notification;

class NotificationService
{
    /**
     * Send a notification to all superadmins.
     *
     * @param Notification $notification
     * @return void
     */
    public static function notify<PERSON><PERSON>rad<PERSON>(Notification $notification): void
    {
        $superadmins = User::where('role', 'superadmin')->get();
        foreach ($superadmins as $admin) {
            $admin->notify($notification);
        }
    }

    /**
     * Send a notification to all users of a specific agency.
     *
     * @param int $agencyId
     * @param Notification $notification
     * @return void
     */
    public static function notifyAgencyUsers(int $agencyId, Notification $notification): void
    {
        $agencyUsers = User::where('agency_id', $agencyId)->get();
        foreach ($agencyUsers as $user) {
            $user->notify($notification);
        }
    }

    /**
     * Send a notification to all agency admins of a specific agency.
     *
     * @param int $agencyId
     * @param Notification $notification
     * @return void
     */
    public static function notifyAgencyAdmins(int $agencyId, Notification $notification): void
    {
        $agencyAdmins = User::where('agency_id', $agencyId)
            ->where('role', 'agency_admin')
            ->get();
            
        foreach ($agencyAdmins as $admin) {
            $admin->notify($notification);
        }
    }

    /**
     * Send a system alert to all superadmins.
     *
     * @param string $title
     * @param string $message
     * @param string $level
     * @param string|null $actionText
     * @param string|null $actionUrl
     * @return void
     */
    public static function sendSystemAlert(
        string $title,
        string $message,
        string $level = 'info',
        ?string $actionText = null,
        ?string $actionUrl = null
    ): void {
        $notification = new SystemAlert($title, $message, $level, $actionText, $actionUrl);
        self::notifySuperadmins($notification);
    }
}
