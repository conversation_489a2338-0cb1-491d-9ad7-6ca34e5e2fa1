<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'agency_id',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    /**
     * Get the agency that the user belongs to.
     */
    public function agency(): BelongsTo
    {
        return $this->belongsTo(Agency::class);
    }

    /**
     * Check if the user is a superadmin.
     */
    public function isSuperAdmin(): bool
    {
        return $this->role === 'superadmin';
    }

    /**
     * Check if the user is an admin.
     */
    public function isAdmin(): bool
    {
        return $this->role === 'admin' || $this->role === 'superadmin';
    }

    /**
     * Check if the user is an agency admin.
     */
    public function isAgencyAdmin(): bool
    {
        return $this->role === 'agency_admin' && $this->agency_id !== null;
    }

    /**
     * Check if the user is an agency user.
     */
    public function isAgencyUser(): bool
    {
        return $this->agency_id !== null;
    }

    /**
     * Get the reservations for the user.
     */
    public function reservations()
    {
        return $this->hasMany(Reservation::class);
    }

    /**
     * Get the groups that the user belongs to.
     */
    public function groups(): BelongsToMany
    {
        return $this->belongsToMany(Group::class, 'group_users')
            ->withPivot('is_guide')
            ->withTimestamps();
    }

    /**
     * Get the groups where the user is a guide.
     */
    public function guidedGroups()
    {
        return $this->groups()->wherePivot('is_guide', true);
    }

    /**
     * Check if the user is a guide in any group or has guide role.
     */
    public function isGuide(): bool
    {
        return $this->role === 'guide' || $this->guidedGroups()->exists();
    }

    /**
     * Check if the user is a guide in the specified group.
     */
    public function isGuideInGroup(Group $group): bool
    {
        return $this->guidedGroups()->where('group_id', $group->id)->exists();
    }
}
