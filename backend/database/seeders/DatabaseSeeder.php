<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Seed agencies first
        $this->call(AgencySeeder::class);

        // Create a superadmin user
        User::factory()->create([
            'name' => 'Super Admin',
            'email' => '<EMAIL>',
            'role' => 'superadmin',
        ]);

        // Create a regular user for testing
        User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'role' => 'user',
        ]);

        User::factory()->create([
            'name' => 'Test Agency User',
            'email' => '<EMAIL>',
            'role' => 'agency',
            'agency_id' => 1,
        ]);

        $this->call(NewsSeeder::class);
        $this->call(EventSeeder::class);
        $this->call(LocationSeeder::class);
        $this->call(ExperiencesTableSeeder::class);
        $this->call(RouteSeeder::class);
        $this->call(GroupSeeder::class);
    }
}
