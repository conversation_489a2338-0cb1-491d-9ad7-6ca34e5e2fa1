<?php

namespace Database\Seeders;

use App\Models\Experience;
use Illuminate\Database\Seeder;

class ExperiencesTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create sample experiences
        Experience::create([
            'title' => 'Alcornocales con ebike',
            'description' => 'Recorre los alcornocales en bicicleta eléctrica y descubre la belleza natural de este ecosistema único.',
            'short_description' => 'El más grande alcornocal de la península ibérica y uno de los más importantes del mundo.',
            'location_id' => null,
            'agency_id' => null,
            'type' => 'activity',
            'duration' => '3 horas',
            'distance' => '12 km',
            'difficulty' => 'moderate',
            'price' => 25.00,
            'image' => 'experiences/alcornocales.jpeg',
            'start_date' => '2025-03-30',
            'end_date' => null,
            'is_featured' => true,
            'is_active' => true,
        ]);

        Experience::create([
            'title' => 'Cor de suro',
            'description' => 'Visita esta fábrica tradicional de corcho y aprende sobre el proceso de fabricación y su historia.',
            'short_description' => 'Descubre el proceso de fabricación del corcho y su historia en esta fábrica tradicional.',
            'location_id' => null,
            'agency_id' => null,
            'type' => 'museum',
            'duration' => '1 hora',
            'distance' => '5 km',
            'difficulty' => 'easy',
            'price' => 0,
            'image' => 'experiences/cordesuro.png',
            'start_date' => null,
            'end_date' => null,
            'is_featured' => false,
            'is_active' => true,
        ]);

        Experience::create([
            'title' => 'Hotel Casa Convento',
            'description' => 'Alojamiento único que destaca por su arquitectura sostenible, utilizando el corcho como material principal en su construcción.',
            'short_description' => 'Alojamiento sostenible construido con materiales de corcho, ofreciendo una experiencia única.',
            'location_id' => null,
            'agency_id' => null,
            'type' => 'hotel',
            'duration' => null,
            'distance' => '8 km',
            'difficulty' => null,
            'price' => 85.00,
            'image' => 'experiences/hotelcasacormoran.jpeg',
            'start_date' => null,
            'end_date' => null,
            'is_featured' => true,
            'is_active' => true,
        ]);
    }
}
